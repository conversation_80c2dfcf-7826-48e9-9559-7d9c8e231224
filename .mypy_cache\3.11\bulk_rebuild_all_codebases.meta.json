{"data_mtime": 1752753629, "dep_lines": [7, 8, 9, 10, 11, 210, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "json", "time", "re", "typing", "embedding_config", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "http", "http.cookiejar", "json.decoder", "requests.auth", "requests.exceptions", "requests.models", "types", "typing_extensions"], "hash": "dd5bdc6bdec5d23db5d78c757ed1fbc1c36f0974", "id": "bulk_rebuild_all_codebases", "ignore_all": false, "interface_hash": "42f0e055d78a296ca629ae1085072cb633771d57", "mtime": 1752759008, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\bulk_rebuild_all_codebases.py", "plugin_data": null, "size": 15729, "suppressed": [], "version_id": "1.15.0"}