#!/usr/bin/env python3
"""
Test script for the Web Management Server
"""

import requests
import time
import sys

def test_web_management_server():
    """Test the web management server functionality"""
    print("🧪 TESTING WEB MANAGEMENT SERVER")
    print("=" * 50)
    
    base_url = "http://localhost:5003"
    
    # Test 1: Dashboard Access
    print("\n📋 TEST 1: DASHBOARD ACCESS")
    print("-" * 30)
    
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        print(f"Dashboard: HTTP {response.status_code}")
        if response.status_code == 200:
            print("✅ Dashboard accessible")
            dashboard_ok = True
        else:
            print("❌ Dashboard not accessible")
            dashboard_ok = False
    except Exception as e:
        print(f"❌ Dashboard error: {e}")
        dashboard_ok = False
    
    # Test 2: API Endpoints
    print("\n📋 TEST 2: API ENDPOINTS")
    print("-" * 30)
    
    api_endpoints = [
        "/api/health",
        "/api/codebases", 
        "/api/gpu",
        "/api/metrics"
    ]
    
    api_results = []
    for endpoint in api_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"{endpoint}: HTTP {response.status_code}")
            api_results.append(response.status_code == 200)
        except Exception as e:
            print(f"{endpoint}: ERROR - {e}")
            api_results.append(False)
    
    # Test 3: Control Endpoints
    print("\n📋 TEST 3: CONTROL ENDPOINTS")
    print("-" * 30)
    
    # Test codebase selection
    try:
        response = requests.post(f"{base_url}/api/control/select_codebase", 
                               json={"codebase_name": "utils"}, timeout=10)
        print(f"Select codebase: HTTP {response.status_code}")
        control_ok = response.status_code == 200
    except Exception as e:
        print(f"Select codebase: ERROR - {e}")
        control_ok = False
    
    # Test query
    try:
        response = requests.post(f"{base_url}/api/test/query", json={
            "query": "test query",
            "codebase_name": "utils",
            "n_results": 3
        }, timeout=15)
        print(f"Test query: HTTP {response.status_code}")
        query_ok = response.status_code == 200
    except Exception as e:
        print(f"Test query: ERROR - {e}")
        query_ok = False
    
    # Results Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Dashboard Access", dashboard_ok),
        ("API Health", api_results[0] if len(api_results) > 0 else False),
        ("API Codebases", api_results[1] if len(api_results) > 1 else False),
        ("API GPU", api_results[2] if len(api_results) > 2 else False),
        ("API Metrics", api_results[3] if len(api_results) > 3 else False),
        ("Control Functions", control_ok),
        ("Query Testing", query_ok)
    ]
    
    passed = sum(1 for _, ok in tests if ok)
    total = len(tests)
    
    print(f"✅ Tests Passed: {passed}/{total}")
    print(f"📊 Success Rate: {(passed/total)*100:.1f}%")
    
    print(f"\n📋 Test Results:")
    for test_name, ok in tests:
        status_icon = "✅" if ok else "❌"
        print(f"   {status_icon} {test_name}")
    
    # Overall Assessment
    print(f"\n🎯 ASSESSMENT:")
    if passed == total:
        print("🟢 PERFECT - Web Management Server fully functional!")
        return True
    elif passed >= total * 0.8:
        print("🟡 GOOD - Web Management Server mostly functional!")
        return True
    else:
        print("🔴 ISSUES - Web Management Server has problems!")
        return False

def main():
    """Main test function"""
    print("🔧 Starting Web Management Server test...")
    print("⚠️ Make sure both servers are running:")
    print("   1. Code Analyzer Server on port 5002")
    print("   2. Web Management Server on port 5003")
    print()
    
    # Wait a moment for servers to be ready
    time.sleep(2)
    
    success = test_web_management_server()
    
    if success:
        print(f"\n🎉 WEB MANAGEMENT SERVER TEST SUCCESSFUL!")
        print("✅ Dashboard is operational")
        print("✅ API endpoints working")
        print("✅ Control functions available")
        print(f"\n🔗 Access dashboard at: http://localhost:5003")
    else:
        print(f"\n⚠️ WEB MANAGEMENT SERVER NEEDS ATTENTION")
        print("❌ Some functionality not working")
        print("🔧 Check server logs for details")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ TEST CRASHED: {e}")
        sys.exit(1)
