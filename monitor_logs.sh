#!/bin/bash
# Log monitoring script for Code Analyzer Docker container

set -e

CONTAINER_NAME="code-analyzer-server"

echo "📊 CODE ANALYZER LOG MONITOR"
echo "============================="

# Check if container is running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ Container '$CONTAINER_NAME' is not running"
    echo "   Start it with: docker-compose up -d"
    exit 1
fi

echo "✅ Container '$CONTAINER_NAME' is running"

# Function to show log file info
show_log_info() {
    echo ""
    echo "📁 LOG DIRECTORY STRUCTURE:"
    echo "----------------------------"
    docker exec "$CONTAINER_NAME" ls -la /var/log/supervisor/ 2>/dev/null || {
        echo "❌ Log directory not accessible"
        return 1
    }
}

# Function to show supervisor status
show_supervisor_status() {
    echo ""
    echo "🔧 SUPERVISOR STATUS:"
    echo "---------------------"
    docker exec "$CONTAINER_NAME" supervisorctl status 2>/dev/null || {
        echo "❌ Supervisor not accessible"
        return 1
    }
}

# Function to show recent logs
show_recent_logs() {
    local service=$1
    local lines=${2:-20}
    
    echo ""
    echo "📋 RECENT $service LOGS (last $lines lines):"
    echo "$(printf '%.0s-' {1..50})"
    
    # Show stdout logs
    echo "📤 STDOUT:"
    docker exec "$CONTAINER_NAME" tail -n "$lines" "/var/log/supervisor/$service.out.log" 2>/dev/null || {
        echo "   No stdout logs available"
    }
    
    echo ""
    echo "📥 STDERR:"
    docker exec "$CONTAINER_NAME" tail -n "$lines" "/var/log/supervisor/$service.err.log" 2>/dev/null || {
        echo "   No stderr logs available"
    }
}

# Function to follow logs in real-time
follow_logs() {
    local service=$1
    
    echo ""
    echo "👁️ FOLLOWING $service LOGS (Ctrl+C to stop):"
    echo "$(printf '%.0s-' {1..50})"
    
    # Follow both stdout and stderr
    docker exec "$CONTAINER_NAME" bash -c "
        tail -f /var/log/supervisor/$service.out.log /var/log/supervisor/$service.err.log 2>/dev/null
    " || {
        echo "❌ Cannot follow logs for $service"
    }
}

# Main menu
show_menu() {
    echo ""
    echo "🎛️ LOG MONITORING OPTIONS:"
    echo "=========================="
    echo "1. Show log directory structure"
    echo "2. Show supervisor status"
    echo "3. Show recent Code Analyzer logs"
    echo "4. Show recent Web Management logs"
    echo "5. Show recent Supervisor logs"
    echo "6. Follow Code Analyzer logs (real-time)"
    echo "7. Follow Web Management logs (real-time)"
    echo "8. Follow all logs (real-time)"
    echo "9. Show all recent logs"
    echo "0. Exit"
    echo ""
    read -p "Select option (0-9): " choice
}

# Process menu choice
process_choice() {
    case $choice in
        1)
            show_log_info
            ;;
        2)
            show_supervisor_status
            ;;
        3)
            show_recent_logs "code-analyzer-server"
            ;;
        4)
            show_recent_logs "web-management-server"
            ;;
        5)
            echo ""
            echo "📋 RECENT SUPERVISOR LOGS:"
            echo "-------------------------"
            docker exec "$CONTAINER_NAME" tail -n 20 "/var/log/supervisor/supervisord.log" 2>/dev/null || {
                echo "   No supervisor logs available"
            }
            ;;
        6)
            follow_logs "code-analyzer-server"
            ;;
        7)
            follow_logs "web-management-server"
            ;;
        8)
            echo ""
            echo "👁️ FOLLOWING ALL LOGS (Ctrl+C to stop):"
            echo "$(printf '%.0s-' {1..50})"
            docker exec "$CONTAINER_NAME" bash -c "
                tail -f /var/log/supervisor/*.log 2>/dev/null
            " || {
                echo "❌ Cannot follow logs"
            }
            ;;
        9)
            show_recent_logs "code-analyzer-server" 10
            show_recent_logs "web-management-server" 10
            echo ""
            echo "📋 RECENT SUPERVISOR LOGS:"
            echo "-------------------------"
            docker exec "$CONTAINER_NAME" tail -n 10 "/var/log/supervisor/supervisord.log" 2>/dev/null || {
                echo "   No supervisor logs available"
            }
            ;;
        0)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please select 0-9."
            ;;
    esac
}

# Quick status check
quick_status() {
    echo ""
    echo "⚡ QUICK STATUS CHECK:"
    echo "====================="
    
    # Check if services are responding
    if curl -f -s http://localhost:5002/health > /dev/null; then
        echo "✅ Code Analyzer Server (5002): Responding"
    else
        echo "❌ Code Analyzer Server (5002): Not responding"
    fi
    
    if curl -f -s http://localhost:5003/api/health > /dev/null; then
        echo "✅ Web Management Interface (5003): Responding"
    else
        echo "❌ Web Management Interface (5003): Not responding"
    fi
    
    # Show supervisor status
    show_supervisor_status
}

# Main execution
main() {
    # Show initial status
    quick_status
    
    # Interactive menu loop
    while true; do
        show_menu
        process_choice
        
        # Pause before showing menu again (except for real-time options)
        if [[ $choice != "6" && $choice != "7" && $choice != "8" ]]; then
            echo ""
            read -p "Press Enter to continue..."
        fi
    done
}

# Handle script arguments
case "${1:-}" in
    --status)
        quick_status
        ;;
    --follow-analyzer)
        follow_logs "code-analyzer-server"
        ;;
    --follow-web)
        follow_logs "web-management-server"
        ;;
    --follow-all)
        echo "👁️ FOLLOWING ALL LOGS (Ctrl+C to stop):"
        docker exec "$CONTAINER_NAME" tail -f /var/log/supervisor/*.log 2>/dev/null
        ;;
    --recent)
        show_recent_logs "code-analyzer-server" 20
        show_recent_logs "web-management-server" 20
        ;;
    --help)
        echo "Usage: $0 [option]"
        echo ""
        echo "Options:"
        echo "  --status         Show quick status check"
        echo "  --follow-analyzer Follow Code Analyzer logs"
        echo "  --follow-web     Follow Web Management logs"
        echo "  --follow-all     Follow all logs"
        echo "  --recent         Show recent logs from all services"
        echo "  --help           Show this help"
        echo ""
        echo "Run without arguments for interactive menu"
        ;;
    "")
        main
        ;;
    *)
        echo "❌ Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
