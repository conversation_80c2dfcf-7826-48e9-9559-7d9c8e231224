# Updated docker-compose.yml section for code-analyzer-server with web management

  code-analyzer-server:
    container_name: code-analyzer-server
    build:
      context: ./code_analyzer_server # Code analyzer source is in subdirectory
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5002:5002" # Enhanced Code Analyzer server
      - "5003:5003" # Web Management Interface
    environment:
      - LOG_LEVEL=info
      - OLLAMA_HOST=http://ollama:11434  # Connect to existing Ollama
      - CHROMA_DB_BASE_PATH=/app/chroma_db
      - SOURCE_CODE_BASE_PATH=/app/source_code
      - USE_OLLAMA_EMBEDDINGS=true
      - DEBUG=true
      - PYTHONUNBUFFERED=1  # Ensure immediate output to logs
      - PYTHONIOENCODING=utf-8
      # Enhanced Framework Environment Variables
      - FRAMEWORK_MODE=production
      - GPU_DISCOVERY_ENABLED=true
      - LANGUAGE_REGISTRY_CACHE=true
      - PROCESSING_PIPELINE_PARALLEL=true
      # Web Management Environment Variables
      - CODE_ANALYZER_BASE_URL=http://localhost:5002
      - WEB_MANAGEMENT_PORT=5003
    volumes:
      - chroma_db:/app/chroma_db # Persist ChromaDB data
      - ./source_code:/app/source_code:ro # Mount source code read-only (at same level as docker-compose.yml)
      - supervisor_logs:/var/log/supervisor # Persist supervisor logs
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:5002/health && curl -f http://localhost:5003/api/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s

volumes:
  chroma_db:
  supervisor_logs:
