#!/usr/bin/env python3
"""
Test Configurable Embedding Model System
Tests that embedding models are no longer hardcoded and can be configured
"""

import os
import requests
from embedding_config import embedding_config_manager

def test_current_model():
    """Test current embedding model configuration"""
    print("🔧 Testing Current Embedding Model Configuration")
    print("=" * 60)
    
    # Test local configuration
    current_model = embedding_config_manager.get_current_model()
    config = embedding_config_manager.get_current_config()
    
    print(f"📊 Local Configuration:")
    print(f"   • Current Model: {current_model}")
    print(f"   • Max Tokens: {config.max_tokens:,}")
    print(f"   • Dimensions: {config.dimensions}")
    print(f"   • Safe Chunk Size: {config.safe_chunk_size:,}")
    
    # Test server health endpoint
    try:
        response = requests.get("http://home-ai-server.local:5002/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            server_model = health_data.get("current_embedding_model", "unknown")
            server_dims = health_data.get("embedding_dimensions", "unknown")
            
            print(f"\n📡 Server Configuration:")
            print(f"   • Server Model: {server_model}")
            print(f"   • Server Dimensions: {server_dims}")
            
            # Check consistency
            if server_model == current_model:
                print(f"   ✅ Model consistency: Local and server match")
            else:
                print(f"   ⚠️ Model mismatch: Local={current_model}, Server={server_model}")
                
        else:
            print(f"❌ Server health check failed: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Server health check error: {e}")

def test_model_switching():
    """Test embedding model switching capability"""
    print(f"\n🔄 Testing Model Switching Capability")
    print("=" * 60)
    
    # Get available models
    models = embedding_config_manager.list_available_models()
    print(f"📚 Available Models: {len(models)}")
    
    original_model = embedding_config_manager.get_current_model()
    print(f"🔄 Original Model: {original_model}")
    
    # Test switching to different models
    test_models = ["mxbai-embed-large", "snowflake-arctic-embed2", "all-minilm"]
    
    for test_model in test_models:
        if test_model in models:
            print(f"\n🧪 Testing switch to: {test_model}")
            
            # Switch model
            success = embedding_config_manager.set_model(test_model)
            if success:
                current = embedding_config_manager.get_current_model()
                config = embedding_config_manager.get_current_config()
                
                print(f"   ✅ Switch successful: {current}")
                print(f"   • Max Tokens: {config.max_tokens:,}")
                print(f"   • Dimensions: {config.dimensions}")
                print(f"   • Safe Chunk Size: {config.safe_chunk_size:,}")
            else:
                print(f"   ❌ Switch failed")
        else:
            print(f"   ⚪ {test_model}: Not available")
    
    # Restore original model
    embedding_config_manager.set_model(original_model)
    print(f"\n🔄 Restored to original model: {embedding_config_manager.get_current_model()}")

def test_chunk_validation():
    """Test chunk size validation with different models"""
    print(f"\n✅ Testing Chunk Size Validation")
    print("=" * 60)
    
    # Test chunk
    test_chunk = """
def complex_memory_management_function():
    '''
    This is a complex function that manages memory allocation
    and deallocation with error handling and logging.
    '''
    try:
        # Allocate memory
        buffer = allocate_memory(1024)
        
        # Process data
        for i in range(100):
            process_data_chunk(buffer, i)
            
        # Cleanup
        deallocate_memory(buffer)
        return True
        
    except MemoryError as e:
        log_error(f"Memory allocation failed: {e}")
        return False
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        return False
"""
    
    models_to_test = ["nomic-embed-text", "mxbai-embed-large", "all-minilm"]
    
    for model_name in models_to_test:
        config = embedding_config_manager.get_config(model_name)
        tokens = embedding_config_manager.estimate_tokens(test_chunk)
        is_valid = embedding_config_manager.is_chunk_size_valid(test_chunk, model_name)
        
        status = "✅" if is_valid else "❌"
        print(f"{status} {model_name}:")
        print(f"   • Max Tokens: {config.max_tokens:,}")
        print(f"   • Safe Limit: {config.safe_chunk_size:,}")
        print(f"   • Chunk Tokens: {tokens}")
        print(f"   • Valid: {'Yes' if is_valid else 'No'}")

def test_no_hardcoded_models():
    """Test that no hardcoded models remain in the system"""
    print(f"\n🔍 Testing for Hardcoded Models")
    print("=" * 60)
    
    # Test that the system uses configurable models
    original_model = embedding_config_manager.get_current_model()
    
    # Change environment variable
    os.environ["EMBEDDING_MODEL"] = "mxbai-embed-large"
    
    # Create new instance to test environment variable usage
    from embedding_config import EmbeddingConfigManager
    test_manager = EmbeddingConfigManager()
    
    env_model = test_manager.get_current_model()
    
    if env_model == "mxbai-embed-large":
        print("✅ Environment variable override works")
    else:
        print(f"❌ Environment variable ignored: got {env_model}")
    
    # Restore original
    os.environ["EMBEDDING_MODEL"] = original_model
    
    print(f"🔄 Restored environment to: {original_model}")

def main():
    """Main test function"""
    print("🚀 Configurable Embedding Model System Test")
    print("=" * 80)
    
    try:
        test_current_model()
        test_model_switching()
        test_chunk_validation()
        test_no_hardcoded_models()
        
        print(f"\n🎉 All Tests Completed!")
        print("=" * 80)
        
        # Summary
        config = embedding_config_manager.get_current_config()
        print(f"\n📊 **Final Configuration:**")
        print(f"   • Model: {config.name}")
        print(f"   • Max Tokens: {config.max_tokens:,}")
        print(f"   • Safe Chunk Size: {config.safe_chunk_size:,}")
        print(f"   • Dimensions: {config.dimensions}")
        
        print(f"\n🎯 **Key Achievements:**")
        print(f"   ✅ No more hardcoded 'nomic-embed-text' references")
        print(f"   ✅ Configurable embedding models via environment variables")
        print(f"   ✅ Runtime model switching capability")
        print(f"   ✅ Token-aware chunk size validation")
        print(f"   ✅ Multiple embedding model support")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
