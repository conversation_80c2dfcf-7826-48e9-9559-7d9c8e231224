#!/usr/bin/env python3
"""
Quick test for LYNN-PC model with shorter timeout
"""

import requests
import json

def quick_test():
    print("🚀 Quick LYNN-PC Test")
    print("=" * 30)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "LYNN-PC.llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Simple test without tool first
    print("🧪 Test 1: Simple query without tool")
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": "Hello, are you working?"}],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
            print(f"✅ Model responds: {content[:100]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False
    
    # Test with tool
    print(f"\n🧪 Test 2: Query with tool (30s timeout)")
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": "list codebases"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"✅ Response received")
            print(f"   Tool calls: {len(tool_calls)}")
            print(f"   Content: {content[:150]}...")
            
            # Check for real vs generic response
            real_indicators = ["utils", "z80emu", "library-management-system"]
            generic_indicators = ["wordpress", "react", "popular"]
            
            found_real = any(ind.lower() in content.lower() for ind in real_indicators)
            found_generic = any(ind.lower() in content.lower() for ind in generic_indicators)
            
            if tool_calls or found_real:
                print("🎉 TOOL IS WORKING!")
                return True
            elif found_generic:
                print("❌ Tool not working - generic response")
                return False
            else:
                print("❓ Unclear response")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Timeout - tool might be working but slow")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print(f"\n🎉 SUCCESS: LYNN-PC model with Code Analyzer tool is working!")
    else:
        print(f"\n❌ ISSUE: Tool needs further debugging")
        print(f"💡 Try:")
        print(f"1. Check OpenWebUI logs")
        print(f"2. Test with local llama3:latest for comparison")
        print(f"3. Verify network connectivity between lynn-pc and home-ai-server")
