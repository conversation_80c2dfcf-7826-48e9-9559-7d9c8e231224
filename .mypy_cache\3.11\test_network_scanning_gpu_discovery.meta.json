{"data_mtime": 1752666045, "dep_lines": [6, 7, 8, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio", "requests", "sys", "gpu_infrastructure", "builtins", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "http", "http.cookiejar", "requests.auth", "requests.models", "types", "typing"], "hash": "2ded472f0134b27d518dfb08491d5d2cbc854b78", "id": "test_network_scanning_gpu_discovery", "ignore_all": false, "interface_hash": "a9ff951e54790fb34e28392752a82b92ef4f133e", "mtime": 1752666535, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_network_scanning_gpu_discovery.py", "plugin_data": null, "size": 8708, "suppressed": [], "version_id": "1.15.0"}