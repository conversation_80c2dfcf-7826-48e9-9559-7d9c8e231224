#!/usr/bin/env python3
"""
Test the bulk rebuild statistics parsing fix
"""

import requests
import json
from bulk_rebuild_all_codebases import parse_rebuild_statistics

def test_parsing_fix():
    """Test the fixed parsing logic"""
    print("🧪 Testing Bulk Rebuild Statistics Parsing Fix")
    print("=" * 60)
    
    # Test with utils codebase data
    try:
        response = requests.post(
            'http://home-ai-server.local:5002/tools/rebuild_codebase', 
            json={'codebase_name': 'utils'}, 
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            
            result_text = data.get('result', '')
            processing_stats = data.get('processing_stats', {})
            deletion_stats = data.get('deletion_stats', {})
            
            # Parse with fixed logic
            stats = parse_rebuild_statistics(result_text, processing_stats, deletion_stats, 'utils')
            
            print(f"📊 **Parsed Statistics:**")
            print(f"   • Chunk count: {stats['chunks']}")
            print(f"   • Languages: {stats['languages']}")
            print(f"   • Tree-sitter: {stats['tree_sitter_info']}")
            print(f"   • Processing time: {stats['processing_time']}")
            
            # Show the raw data for comparison
            print(f"\n🔍 **Raw Processing Stats:**")
            if processing_stats:
                type_dist = processing_stats.get('type_distribution', {})
                semantic_tags = processing_stats.get('semantic_tag_frequency', {})
                
                print(f"   • Type distribution: {type_dist}")
                print(f"   • Type sum: {sum(type_dist.values()) if type_dist else 0}")
                print(f"   • Semantic tags: {semantic_tags}")
                print(f"   • Semantic sum: {sum(semantic_tags.values()) if semantic_tags else 0}")
            
            # Verify the fix
            expected_chunks = 302
            actual_chunks = stats['chunks']
            
            if actual_chunks == expected_chunks:
                print(f"\n✅ **FIX SUCCESSFUL!** Chunk count is correct: {actual_chunks}")
            else:
                print(f"\n❌ **FIX FAILED!** Expected {expected_chunks}, got {actual_chunks}")
                
        else:
            print(f"❌ API request failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_parsing_fix()
