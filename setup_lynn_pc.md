# Lynn-PC Setup Guide for Code Analyzer

## 🎯 Goal
Set up a complete Code Analyzer environment on lynn-pc with RTX 3050 GPU support.

## 📋 Prerequisites
- Windows with WSL2 or Linux
- Docker with GPU support
- RTX 3050 drivers installed
- Git

## 🚀 Step-by-Step Setup

### Step 1: Clone Repository
```bash
# Choose a location (e.g., C:\code-analyzer or ~/code-analyzer)
git clone <repository_url> code-analyzer-lynn
cd code-analyzer-lynn
```

### Step 2: Copy Source Code
Copy the source_code directory from home-ai-server:
```bash
# Create source_code directory
mkdir source_code

# Copy codebases (you'll need to transfer these from home-ai-server):
# - library-management-system
# - utils  
# - z80emu
# - test_project
# - etc.
```

### Step 3: Configure Docker Compose
Update docker-compose.yml for lynn-pc:

```yaml
version: '3.8'

services:
  code-analyzer-server:
    build:
      context: ./code_analyzer_server
      dockerfile: Dockerfile
    ports:
      - "5002:5002"
    volumes:
      - ./source_code:/app/source_code
      - ./vector_db:/app/vector_db
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    depends_on:
      - ollama
      - chromadb

  web-management-server:
    build:
      context: ./code_analyzer_server
      dockerfile: Dockerfile
    command: python web_management_server.py
    ports:
      - "5003:5003"
    volumes:
      - ./source_code:/app/source_code
      - ./vector_db:/app/vector_db
    environment:
      - CODE_ANALYZER_BASE_URL=http://code-analyzer-server:5002
    depends_on:
      - code-analyzer-server

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  chromadb:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0

  openwebui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "8080:8080"
    volumes:
      - openwebui_data:/app/backend/data
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - WEBUI_SECRET_KEY=your-secret-key-here
    depends_on:
      - ollama

volumes:
  ollama_data:
  chroma_data:
  openwebui_data:
```

### Step 4: Start Services
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f code-analyzer-server
```

### Step 5: Install Ollama Models
```bash
# Install embedding model
docker exec -it <ollama_container> ollama pull nomic-embed-text

# Install LLM models
docker exec -it <ollama_container> ollama pull llama3:latest
docker exec -it <ollama_container> ollama pull deepseek-coder:6.7b
```

### Step 6: Install OpenWebUI Tool
1. Open http://localhost:8080
2. Go to Workspace → Tools
3. Add new tool
4. Copy contents of `open_webui_code_analyzer_tool.py`
5. Update server URL to: `http://localhost:5002`
6. Save and enable for models

### Step 7: Test Setup
```bash
# Test code analyzer server
curl http://localhost:5002/health

# Test web management
curl http://localhost:5003/api/health

# Test OpenWebUI
curl http://localhost:8080
```

## 🧪 Testing Checklist

### Basic Functionality
- [ ] All containers running
- [ ] RTX 3050 detected in containers
- [ ] Ollama models installed
- [ ] Code analyzer server responds
- [ ] Web management interface accessible
- [ ] OpenWebUI accessible

### Code Analysis
- [ ] Create codebase (library-management-system)
- [ ] Index source code
- [ ] Test queries through OpenWebUI
- [ ] Verify tool responses
- [ ] Check GPU utilization

### Tool Integration
- [ ] Tool installed in OpenWebUI
- [ ] Tool enabled for models
- [ ] Tool returns real codebase data
- [ ] No generic responses

## 🔧 Troubleshooting

### GPU Issues
```bash
# Check GPU in container
docker exec -it <container> nvidia-smi

# Check CUDA
docker exec -it <container> nvcc --version
```

### Network Issues
```bash
# Check container networking
docker network ls
docker network inspect <network_name>
```

### Service Issues
```bash
# Check logs
docker-compose logs <service_name>

# Restart service
docker-compose restart <service_name>
```

## 🎉 Expected Results

After setup, you should have:
- ✅ Complete code analyzer environment on lynn-pc
- ✅ RTX 3050 GPU acceleration
- ✅ Local OpenWebUI with working tool
- ✅ All codebases indexed and searchable
- ✅ No network connectivity issues

This gives you a clean, independent environment for testing and development!
