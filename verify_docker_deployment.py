#!/usr/bin/env python3
"""
Verify Docker deployment with both Code Analyzer Server and Web Management Interface
"""

import requests
import time
import sys

def test_docker_deployment():
    """Test both services in the Docker container"""
    print("🐳 TESTING DOCKER DEPLOYMENT")
    print("=" * 50)
    
    base_analyzer_url = "http://home-ai-server.local:5002"
    base_web_url = "http://home-ai-server.local:5003"
    
    print(f"Code Analyzer Server: {base_analyzer_url}")
    print(f"Web Management Interface: {base_web_url}")
    
    # Test 1: Code Analyzer Server Health
    print("\n📋 TEST 1: CODE ANALYZER SERVER")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_analyzer_url}/health", timeout=10)
        print(f"Health endpoint: HTTP {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Service: {data.get('code_analyzer_service', 'unknown')}")
            print(f"   ✅ Version: {data.get('version', 'unknown')}")
            analyzer_ok = True
        else:
            print(f"   ❌ Health check failed")
            analyzer_ok = False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        analyzer_ok = False
    
    # Test 2: Web Management Interface
    print("\n📋 TEST 2: WEB MANAGEMENT INTERFACE")
    print("-" * 40)
    
    try:
        # Test dashboard
        response = requests.get(f"{base_web_url}/", timeout=10)
        print(f"Dashboard: HTTP {response.status_code}")
        dashboard_ok = response.status_code == 200
        
        # Test API health
        response = requests.get(f"{base_web_url}/api/health", timeout=10)
        print(f"API Health: HTTP {response.status_code}")
        api_ok = response.status_code == 200
        
        web_ok = dashboard_ok and api_ok
        
        if web_ok:
            print(f"   ✅ Web interface operational")
        else:
            print(f"   ❌ Web interface issues")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        web_ok = False
    
    # Test 3: Integration Test
    print("\n📋 TEST 3: INTEGRATION TEST")
    print("-" * 40)
    
    try:
        # Test web management calling analyzer server
        response = requests.get(f"{base_web_url}/api/codebases", timeout=15)
        print(f"Codebases API: HTTP {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Integration working")
            print(f"   ✅ Codebases: {data.get('total_codebases', 0)}")
            integration_ok = True
        else:
            print(f"   ❌ Integration failed")
            integration_ok = False
            
    except Exception as e:
        print(f"   ❌ Integration error: {e}")
        integration_ok = False
    
    # Test 4: Core Functionality
    print("\n📋 TEST 4: CORE FUNCTIONALITY")
    print("-" * 40)
    
    try:
        # Test analyzer server tools
        response = requests.post(f"{base_analyzer_url}/tools/list_codebases", json={}, timeout=10)
        print(f"List codebases: HTTP {response.status_code}")
        tools_ok = response.status_code == 200
        
        # Test enhanced stats
        response = requests.post(f"{base_analyzer_url}/tools/enhanced_stats", 
                               json={"codebase_name": "utils"}, timeout=10)
        print(f"Enhanced stats: HTTP {response.status_code}")
        enhanced_ok = response.status_code == 200
        
        functionality_ok = tools_ok and enhanced_ok
        
        if functionality_ok:
            print(f"   ✅ Core functionality working")
        else:
            print(f"   ❌ Some functionality issues")
            
    except Exception as e:
        print(f"   ❌ Functionality error: {e}")
        functionality_ok = False
    
    # Results Summary
    print("\n" + "=" * 50)
    print("📊 DOCKER DEPLOYMENT TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Code Analyzer Server", analyzer_ok),
        ("Web Management Interface", web_ok),
        ("Service Integration", integration_ok),
        ("Core Functionality", functionality_ok)
    ]
    
    passed = sum(1 for _, ok in tests if ok)
    total = len(tests)
    
    print(f"✅ Tests Passed: {passed}/{total}")
    print(f"📊 Success Rate: {(passed/total)*100:.1f}%")
    
    print(f"\n📋 Test Results:")
    for test_name, ok in tests:
        status_icon = "✅" if ok else "❌"
        print(f"   {status_icon} {test_name}")
    
    # Access Information
    if passed >= total * 0.75:
        print(f"\n🎉 DEPLOYMENT SUCCESSFUL!")
        print("=" * 30)
        print("🔗 Access Points:")
        print(f"   • Code Analyzer API: {base_analyzer_url}")
        print(f"   • Web Management Dashboard: {base_web_url}")
        print()
        print("📊 Available Features:")
        print("   • Real-time server monitoring")
        print("   • Codebase management interface")
        print("   • GPU infrastructure status")
        print("   • Query testing and performance metrics")
        print("   • Unified logging and health checks")
        
        return True
    else:
        print(f"\n⚠️ DEPLOYMENT NEEDS ATTENTION")
        print("❌ Some services not working properly")
        print("🔧 Check Docker logs: docker logs code-analyzer-server")
        return False

def main():
    """Main test function"""
    print("🔧 Docker Deployment Verification")
    print("⚠️ Make sure the Docker container is running:")
    print("   docker-compose up -d")
    print()
    
    # Wait a moment for services to be ready
    time.sleep(3)
    
    success = test_docker_deployment()
    
    if success:
        print(f"\n🎯 DOCKER DEPLOYMENT VERIFIED!")
        print("✅ Both services operational in single container")
        print("✅ Web management interface accessible")
        print("✅ Service integration working")
    else:
        print(f"\n⚠️ DOCKER DEPLOYMENT ISSUES DETECTED")
        print("❌ Some services not responding")
        print("🔧 Check supervisor status: docker exec code-analyzer-server supervisorctl status")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ TEST CRASHED: {e}")
        sys.exit(1)
