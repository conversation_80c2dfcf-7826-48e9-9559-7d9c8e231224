can we integrate bulk_rebuild_all_codebases.py into the system?  i.e. the web interface and the main api.  if the rebuild all codebases already exists enhance it by using the features we now have in bulk_rebuild_all_codebases.py


















I still see some of the fields in this as unknown.  can we fix that?

curl -X POST http://home-ai-server.local:5002/tools/rebuild_codebase -H "Content-Type: application/json" -d '{"codebase_name": "bookstore"}' --max-time 120
{"success":true,"result":"🔄 **Codebase Rebuild Complete: bookstore**\n\n**Deletion Results:**\n• Chunks deleted: 0\n• Collections removed: 0\n\n**Recreation Results:**\n• Files processed: 4\n• Chunks created: 9\n• Functions discovered: 8\n• Lines of code: 93\n• Languages: typescript, multi\n• Processing time: 2025-07-17T12:29:11.158170\n\n✅ **Vector database successfully rebuilt!**","codebase_name":"bookstore","deletion_stats":{"success":true,"codebase_name":"bookstore","chunkss_deleted":9,"source_directory_exists":true,"source_directory_path":"/app/source_code/bookstore","search_indexes_cleared":true,"metadata_files_cleaned":true},"processing_stats":{"language_distribution":{"typescript":8,"multi":1},"type_distribution":{"unknown":9},"complexity_distribution":{"unknown":9},"quality_distribution":{"unknown":9},"semantic_tag_frequency":{"function":8,"code_implementation":8,"architecture":1,"architectural_pattern":1,"design":1},"total_lines_of_code":93,"average_chunk_size":10.333333333333334,"files_processed":4}}
PS C:\home-repos\openwebui_rag_code_server> 







run the bulk rebuild script again to verify the fixed statistics reporting across all codebases



this still seems wrong :
📊 **Individual Codebase Changes:**
   📈 utils: 302 → 606 (+100.7%)
   📈 go-example-master: 254 → 515 (+102.8%)
   📈 rust-starter-master: 19 → 41 (+115.8%)
   📈 test_project: 4 → 9 (+125.0%)
   📈 z80emu: 770 → 1541 (+100.1%)
   📈 bookstore: 9 → 19 (+111.1%)
   📈 vga-to-spi-tft: 3 → 7 (+133.3%)
   📈 modbus: 268 → 537 (+100.4%)
   📈 TypeScript-Node-Starter-master: 285 → 576 (+102.1%)
   📈 networking_project: 8 → 17 (+112.5%)

   why the large change in chunks