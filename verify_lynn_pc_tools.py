#!/usr/bin/env python3
"""
Verify lynn-pc tool calling is working properly
"""

import requests
import json

def test_lynn_pc_tool_execution():
    """Test that lynn-pc can actually execute tools"""
    print("🧪 Verifying lynn-pc Tool Execution")
    print("=" * 50)
    
    lynn_pc_ollama = "http://************:11434"
    
    # Simple tool that should definitely work
    tools = [{
        'type': 'function',
        'function': {
            'name': 'list_codebases',
            'description': 'List all available codebases for analysis',
            'parameters': {
                'type': 'object',
                'properties': {},
                'required': [],
            },
        },
    }]
    
    messages = [
        {'role': 'user', 'content': 'Please list all available codebases using the list_codebases tool'}
    ]
    
    payload = {
        'model': 'llama3.1:latest',
        'messages': messages,
        'tools': tools,
        'stream': False
    }
    
    print(f"📤 Testing explicit tool request on lynn-pc")
    print(f"🔗 URL: {lynn_pc_ollama}/api/chat")
    
    try:
        response = requests.post(
            f"{lynn_pc_ollama}/api/chat",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            print(f"✅ Response received!")
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            print(f"📝 Content: {content[:200]}...")
            
            if tool_calls:
                print(f"🎉 SUCCESS: lynn-pc is calling tools!")
                for i, tc in enumerate(tool_calls):
                    func = tc.get('function', {})
                    name = func.get('name', 'unknown')
                    args = func.get('arguments', '{}')
                    print(f"   Tool {i+1}: {name}")
                    print(f"   Arguments: {args}")
                return True
            else:
                print(f"⚠️ lynn-pc responded but didn't call tools")
                print(f"   This might be a prompt issue, not a technical issue")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_code_analyzer_server_connection():
    """Test if we can reach the code analyzer server from here"""
    print(f"\n🔗 Testing Code Analyzer Server Connection")
    print("-" * 40)
    
    code_analyzer_url = "http://192.168.0.77:5002"
    
    try:
        # Test the list_codebases endpoint
        response = requests.post(
            f"{code_analyzer_url}/tools/list_codebases",
            json={},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Code analyzer server is reachable!")
            print(f"📊 Response: {str(data)[:200]}...")
            return True
        else:
            print(f"❌ Code analyzer server error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cannot reach code analyzer server: {e}")
        return False

def test_full_workflow():
    """Test the complete workflow: lynn-pc -> tool call -> code analyzer -> response"""
    print(f"\n🔄 Testing Complete Workflow")
    print("-" * 40)
    
    from direct_ollama_code_analyzer import DirectOllamaCodeAnalyzer
    
    analyzer = DirectOllamaCodeAnalyzer("http://************:11434")
    
    # Simple query that should definitely trigger a tool call
    query = "Use the list_codebases tool to show me all available codebases"
    
    print(f"📤 Query: {query}")
    
    result = analyzer.chat_with_tools(query)
    
    print(f"📥 Result: {result[:300]}...")
    
    # Check if we got real codebase data
    real_codebases = ["utils", "z80emu", "go-example-master", "TypeScript-Node-Starter-master"]
    found_real = [cb for cb in real_codebases if cb.lower() in result.lower()]
    
    if found_real:
        print(f"✅ SUCCESS: Found real codebases: {found_real}")
        return True
    else:
        print(f"⚠️ No real codebase data found")
        return False

def main():
    print("🎯 Lynn-PC Tool Calling Verification")
    print("=" * 60)
    
    # Test 1: Basic tool calling
    tool_calling_works = test_lynn_pc_tool_execution()
    
    # Test 2: Code analyzer server connection
    server_reachable = test_code_analyzer_server_connection()
    
    # Test 3: Full workflow
    workflow_works = test_full_workflow()
    
    print(f"\n🎯 VERIFICATION RESULTS")
    print("=" * 60)
    print(f"lynn-pc tool calling:     {'✅ Working' if tool_calling_works else '❌ Not working'}")
    print(f"Code analyzer server:     {'✅ Reachable' if server_reachable else '❌ Not reachable'}")
    print(f"Complete workflow:        {'✅ Working' if workflow_works else '❌ Not working'}")
    
    if all([tool_calling_works, server_reachable, workflow_works]):
        print(f"\n🎉 COMPLETE SUCCESS!")
        print("✅ lynn-pc can successfully use tools for code analysis")
        print("✅ Remote Ollama tool calling is fully functional")
        print("✅ This bypasses all OpenWebUI limitations")
    else:
        print(f"\n⚠️ PARTIAL SUCCESS")
        if tool_calling_works:
            print("✅ Basic tool calling works on lynn-pc")
        if server_reachable:
            print("✅ Code analyzer server is accessible")
        if not workflow_works:
            print("❌ Full workflow needs debugging")

if __name__ == "__main__":
    main()
