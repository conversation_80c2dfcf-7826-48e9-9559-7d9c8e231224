#!/usr/bin/env python3
"""
Check for Chunk Duplication in ChromaDB
Investigates if chunks are being duplicated instead of properly replaced
"""

import requests
import json

def check_chromadb_direct():
    """Check ChromaDB collection directly via Docker"""
    print("🔍 Checking ChromaDB Collections Directly")
    print("=" * 60)
    
    # Test a few codebases
    test_codebases = ["utils", "bookstore", "z80emu"]
    
    for codebase in test_codebases:
        print(f"\n📊 Checking {codebase}:")
        
        # Search for all chunks to get actual count
        try:
            response = requests.post(
                "http://home-ai-server.local:5002/search",
                json={
                    "query": "code",  # Generic query to get many results
                    "codebase_name": codebase,
                    "n_results": 50  # Get more results to see patterns
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])
                total_results = data.get("total_results", len(results))
                
                print(f"   📈 Search Results: {len(results)} returned, {total_results} total")
                
                # Check for duplicates by analyzing metadata
                file_paths = {}
                line_ranges = {}
                chunk_ids = set()
                
                for result in results:
                    metadata = result.get("metadata", {})
                    file_path = metadata.get("file_path", "unknown")
                    start_line = metadata.get("start_line", 0)
                    end_line = metadata.get("end_line", 0)
                    chunk_type = metadata.get("chunk_type", "unknown")
                    
                    # Track file paths
                    if file_path not in file_paths:
                        file_paths[file_path] = 0
                    file_paths[file_path] += 1
                    
                    # Track line ranges for duplication detection
                    line_key = f"{file_path}:{start_line}-{end_line}"
                    if line_key not in line_ranges:
                        line_ranges[line_key] = []
                    line_ranges[line_key].append({
                        "chunk_type": chunk_type,
                        "content_preview": result.get("content", "")[:100]
                    })
                
                # Report findings
                print(f"   📁 Unique Files: {len(file_paths)}")
                print(f"   📍 Unique Line Ranges: {len(line_ranges)}")
                
                # Check for duplicates
                duplicates = {k: v for k, v in line_ranges.items() if len(v) > 1}
                if duplicates:
                    print(f"   ⚠️ POTENTIAL DUPLICATES: {len(duplicates)}")
                    for line_key, chunks in list(duplicates.items())[:3]:  # Show first 3
                        print(f"      🔄 {line_key}: {len(chunks)} chunks")
                        for i, chunk in enumerate(chunks):
                            print(f"         {i+1}. {chunk['chunk_type']}: {chunk['content_preview'][:50]}...")
                else:
                    print(f"   ✅ No duplicates detected in sample")
                
                # Show file distribution
                print(f"   📊 Top Files by Chunk Count:")
                sorted_files = sorted(file_paths.items(), key=lambda x: x[1], reverse=True)
                for file_path, count in sorted_files[:5]:
                    file_name = file_path.split('/')[-1] if '/' in file_path else file_path
                    print(f"      • {file_name}: {count} chunks")
                    
            else:
                print(f"   ❌ Search failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def check_list_vs_search_discrepancy():
    """Check discrepancy between list_codebases and actual search results"""
    print(f"\n🔍 Checking List vs Search Discrepancy")
    print("=" * 60)
    
    # Get list_codebases counts
    try:
        response = requests.get("http://home-ai-server.local:5002/tools/list_codebases", timeout=15)
        if response.status_code == 200:
            result_text = response.json().get("result", "")
            
            # Parse chunk counts from list
            import re
            list_counts = {}
            lines = result_text.split('\n')
            current_codebase = None
            
            for line in lines:
                if line.startswith('**✅ '):
                    current_codebase = line.split('**')[1].replace('✅ ', '').strip()
                elif current_codebase and 'Chunks:' in line:
                    chunk_match = re.search(r'Chunks:\s*(\d+)', line)
                    if chunk_match:
                        list_counts[current_codebase] = int(chunk_match.group(1))
            
            print(f"📋 List Codebases Counts:")
            for codebase, count in list_counts.items():
                print(f"   • {codebase}: {count} chunks")
            
            # Compare with search results
            print(f"\n🔍 Comparing with Search Results:")
            for codebase in ["utils", "bookstore", "z80emu"]:
                list_count = list_counts.get(codebase, 0)
                
                # Get search count
                try:
                    search_response = requests.post(
                        "http://home-ai-server.local:5002/search",
                        json={
                            "query": "code",
                            "codebase_name": codebase,
                            "n_results": 1  # Just need total count
                        },
                        timeout=10
                    )
                    
                    if search_response.status_code == 200:
                        search_data = search_response.json()
                        search_count = search_data.get("total_results", 0)
                        
                        status = "✅" if list_count == search_count else "⚠️"
                        print(f"   {status} {codebase}: List={list_count}, Search={search_count}")
                        
                        if list_count != search_count:
                            print(f"      🔍 Discrepancy: {abs(list_count - search_count)} chunks difference")
                    else:
                        print(f"   ❌ {codebase}: Search failed")
                        
                except Exception as e:
                    print(f"   ❌ {codebase}: Search error - {e}")
                    
        else:
            print(f"❌ List codebases failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ List codebases error: {e}")

def main():
    """Main investigation function"""
    print("🚨 Investigating Potential Chunk Duplication")
    print("=" * 80)
    
    try:
        check_chromadb_direct()
        check_list_vs_search_discrepancy()
        
        print(f"\n🎯 **Investigation Summary:**")
        print(f"   • Checked ChromaDB collections directly")
        print(f"   • Analyzed chunk metadata for duplicates")
        print(f"   • Compared list_codebases vs search results")
        print(f"   • Looked for line range overlaps")
        
        print(f"\n💡 **Next Steps:**")
        print(f"   1. If duplicates found: Check rebuild deletion process")
        print(f"   2. If counts mismatch: Check caching/reporting issues")
        print(f"   3. If chunks reverted: Check persistence mechanism")
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")

if __name__ == "__main__":
    main()
