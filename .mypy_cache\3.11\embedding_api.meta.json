{"data_mtime": **********, "dep_lines": [6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["<PERSON><PERSON><PERSON>", "pydantic", "typing", "embedding_config", "os", "builtins", "_frozen_importlib", "abc", "contextlib", "enum", "fastapi.exceptions", "fastapi.params", "fastapi.routing", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette", "starlette.exceptions", "starlette.responses", "starlette.routing", "typing_extensions"], "hash": "a09705cbd9ff1ba26801f43412dad7d66a312b42", "id": "embedding_api", "ignore_all": false, "interface_hash": "388cef305668355e0fbc57fe17de84ca88b5036c", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\embedding_api.py", "plugin_data": null, "size": 6596, "suppressed": [], "version_id": "1.15.0"}