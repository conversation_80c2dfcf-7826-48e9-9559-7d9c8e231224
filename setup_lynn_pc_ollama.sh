#!/bin/bash
# Setup Ollama on lynn-pc for remote access from home-ai-server

echo "🚀 Setting up Ollama on lynn-pc for remote access"
echo "================================================"

# Check if running on correct machine
echo "📋 System Information:"
echo "Hostname: $(hostname)"
echo "IP Address: $(hostname -I)"
echo "GPU: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits 2>/dev/null || echo 'No NVIDIA GPU detected')"

# Install Ollama if not present
if ! command -v ollama &> /dev/null; then
    echo "📦 Installing Ollama..."
    curl -fsSL https://ollama.ai/install.sh | sh
else
    echo "✅ Ollama already installed"
fi

# Create systemd service for network access
echo "🔧 Configuring Ollama for network access..."

# Create service file
sudo tee /etc/systemd/system/ollama.service > /dev/null <<EOF
[Unit]
Description=Ollama Server
After=network-online.target

[Service]
ExecStart=/usr/local/bin/ollama serve
User=ollama
Group=ollama
Restart=always
RestartSec=3
Environment="OLLAMA_HOST=0.0.0.0"
Environment="OLLAMA_ORIGINS=*"

[Install]
WantedBy=default.target
EOF

# Create ollama user if doesn't exist
if ! id "ollama" &>/dev/null; then
    echo "👤 Creating ollama user..."
    sudo useradd -r -s /bin/false -m -d /usr/share/ollama ollama
fi

# Set permissions
sudo systemctl daemon-reload
sudo systemctl enable ollama
sudo systemctl start ollama

# Wait for service to start
echo "⏳ Waiting for Ollama to start..."
sleep 5

# Check if service is running
if systemctl is-active --quiet ollama; then
    echo "✅ Ollama service is running"
else
    echo "❌ Ollama service failed to start"
    sudo systemctl status ollama
    exit 1
fi

# Install models
echo "📚 Installing models..."

models=("llama3:latest" "deepseek-coder:6.7b" "nomic-embed-text")

for model in "${models[@]}"; do
    echo "📥 Installing $model..."
    ollama pull "$model"
    if [ $? -eq 0 ]; then
        echo "✅ $model installed successfully"
    else
        echo "❌ Failed to install $model"
    fi
done

# Test local access
echo "🧪 Testing local access..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✅ Local access working"
else
    echo "❌ Local access failed"
fi

# Test network access
echo "🌐 Testing network access..."
local_ip=$(hostname -I | awk '{print $1}')
if curl -s http://$local_ip:11434/api/tags > /dev/null; then
    echo "✅ Network access working"
    echo "🔗 Ollama accessible at: http://$local_ip:11434"
else
    echo "❌ Network access failed"
    echo "🔧 You may need to configure firewall:"
    echo "   sudo ufw allow 11434"
fi

# Show firewall status
echo "🔥 Firewall status:"
sudo ufw status | grep 11434 || echo "Port 11434 not explicitly allowed"

# Show installed models
echo "📋 Installed models:"
ollama list

# Show GPU status
echo "🎮 GPU Status:"
nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits 2>/dev/null || echo "No NVIDIA GPU detected"

echo ""
echo "🎉 Setup complete!"
echo "================================================"
echo "📍 lynn-pc Ollama URL: http://$local_ip:11434"
echo "🔧 Add this URL to OpenWebUI on home-ai-server"
echo "🧪 Test with: curl http://$local_ip:11434/api/tags"
echo ""
echo "📋 Next steps:"
echo "1. Add Ollama connection in OpenWebUI"
echo "2. Enable code_analyzer_tool for lynn-pc models"
echo "3. Test with: 'list codebases'"
echo ""
echo "🔍 Troubleshooting:"
echo "- Check firewall: sudo ufw allow 11434"
echo "- Check service: sudo systemctl status ollama"
echo "- Check logs: sudo journalctl -u ollama -f"
