#!/usr/bin/env python3
"""
Setup script for Code Analyzer Web Management Server
Creates necessary directories and installs dependencies
"""

import os
import sys
import subprocess
import json

def create_directories():
    """Create necessary directories"""
    directories = [
        "templates",
        "static",
        "static/css",
        "static/js"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"📁 Directory exists: {directory}")

def check_dependencies():
    """Check and install required dependencies"""
    required_packages = [
        "fastapi",
        "uvicorn[standard]",
        "jinja2",
        "python-multipart",
        "requests"
    ]
    
    print("🔍 Checking dependencies...")
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.split('[')[0])  # Handle packages like uvicorn[standard]
            print(f"✅ {package} - installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("✅ All dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    else:
        print("✅ All dependencies are already installed!")
    
    return True

def create_config_file():
    """Create configuration file"""
    config = {
        "code_analyzer_server": {
            "host": "localhost",
            "port": 5002,
            "base_url": "http://localhost:5002"
        },
        "web_management_server": {
            "host": "0.0.0.0",
            "port": 5003,
            "title": "Code Analyzer Management Interface",
            "auto_refresh_interval": 30
        },
        "features": {
            "server_restart": False,
            "log_monitoring": True,
            "performance_metrics": True,
            "codebase_management": True,
            "query_testing": True
        }
    }
    
    config_file = "web_management_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Created configuration file: {config_file}")

def create_startup_script():
    """Create startup script"""
    startup_script = """#!/bin/bash
# Code Analyzer Web Management Server Startup Script

echo "🚀 Starting Code Analyzer Web Management Server..."

# Check if Code Analyzer Server is running
if curl -s http://localhost:5002/health > /dev/null; then
    echo "✅ Code Analyzer Server is running on port 5002"
else
    echo "⚠️ Code Analyzer Server not detected on port 5002"
    echo "   Make sure the main server is running first"
fi

# Start Web Management Server
echo "🌐 Starting Web Management Interface on port 5003..."
python3 web_management_server.py

echo "🔗 Access the dashboard at: http://localhost:5003"
"""
    
    with open("start_web_management.sh", 'w') as f:
        f.write(startup_script)
    
    # Make executable on Unix systems
    if os.name != 'nt':
        os.chmod("start_web_management.sh", 0o755)
    
    print("✅ Created startup script: start_web_management.sh")

def create_docker_compose_addition():
    """Create Docker Compose addition for the web management server"""
    docker_compose_addition = """
# Add this to your existing docker-compose.yml file

  web-management:
    build: .
    ports:
      - "5003:5003"
    volumes:
      - .:/app
    working_dir: /app
    command: python3 web_management_server.py
    depends_on:
      - code-analyzer-server
    environment:
      - CODE_ANALYZER_BASE_URL=http://code-analyzer-server:5002
    networks:
      - analyzer-network

networks:
  analyzer-network:
    driver: bridge
"""
    
    with open("docker-compose-web-management.yml", 'w') as f:
        f.write(docker_compose_addition.strip())
    
    print("✅ Created Docker Compose addition: docker-compose-web-management.yml")

def create_systemd_service():
    """Create systemd service file"""
    service_content = f"""[Unit]
Description=Code Analyzer Web Management Server
After=network.target
Requires=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={os.getcwd()}
ExecStart={sys.executable} web_management_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    with open("code-analyzer-web-management.service", 'w') as f:
        f.write(service_content)
    
    print("✅ Created systemd service file: code-analyzer-web-management.service")
    print("   To install: sudo cp code-analyzer-web-management.service /etc/systemd/system/")
    print("   To enable: sudo systemctl enable code-analyzer-web-management")
    print("   To start: sudo systemctl start code-analyzer-web-management")

def main():
    """Main setup function"""
    print("🔧 CODE ANALYZER WEB MANAGEMENT SETUP")
    print("=" * 50)
    
    # Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Check dependencies
    print("\n📦 Checking dependencies...")
    if not check_dependencies():
        print("❌ Setup failed due to dependency issues")
        return False
    
    # Create configuration
    print("\n⚙️ Creating configuration...")
    create_config_file()
    
    # Create startup script
    print("\n🚀 Creating startup script...")
    create_startup_script()
    
    # Create Docker Compose addition
    print("\n🐳 Creating Docker integration...")
    create_docker_compose_addition()
    
    # Create systemd service
    print("\n🔧 Creating systemd service...")
    create_systemd_service()
    
    print("\n" + "=" * 50)
    print("✅ SETUP COMPLETE!")
    print("=" * 50)
    
    print("\n🚀 To start the Web Management Server:")
    print("   Option 1: python3 web_management_server.py")
    print("   Option 2: ./start_web_management.sh")
    print("   Option 3: docker-compose -f docker-compose-web-management.yml up")
    
    print("\n🔗 Access the dashboard at:")
    print("   http://localhost:5003")
    print("   http://home-ai-server.local:5003 (if deployed)")
    
    print("\n📋 Features available:")
    print("   • Real-time server health monitoring")
    print("   • Codebase status and management")
    print("   • GPU infrastructure monitoring")
    print("   • Performance metrics dashboard")
    print("   • Query testing interface")
    print("   • Server control panel")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
