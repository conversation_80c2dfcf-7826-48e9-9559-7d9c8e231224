{"data_mtime": 1752768922, "dep_lines": [1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["tree_sitter_language_pack", "tree_sitter", "typing", "os", "embedding_config", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "02904296ed91515e7b570a70c7a2a5d3e9bc404c", "id": "tree_sitter_chunker", "ignore_all": false, "interface_hash": "27a1761f11dcc23d7e475fb107f6bccd0e9c7de1", "mtime": 1752768921, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\tree_sitter_chunker.py", "plugin_data": null, "size": 15775, "suppressed": [], "version_id": "1.15.0"}