#!/usr/bin/env python3
"""
Direct Ollama interface for code analysis with native tool calling
Bypasses OpenWebUI for remote instances
"""

import requests
import json

class DirectOllamaCodeAnalyzer:
    def __init__(self, ollama_url="http://************:11434"):
        self.ollama_url = ollama_url
        self.code_analyzer_url = "http://************:5002"
        
    def create_code_analyzer_tools(self):
        """Create tool definitions for code analysis"""
        return [
            {
                'type': 'function',
                'function': {
                    'name': 'list_codebases',
                    'description': 'List all available codebases for analysis',
                    'parameters': {
                        'type': 'object',
                        'properties': {},
                        'required': [],
                    },
                },
            },
            {
                'type': 'function',
                'function': {
                    'name': 'query_codebase',
                    'description': 'Search for specific code patterns or functions in a codebase',
                    'parameters': {
                        'type': 'object',
                        'properties': {
                            'codebase_name': {
                                'type': 'string',
                                'description': 'Name of the codebase to search in',
                            },
                            'query': {
                                'type': 'string',
                                'description': 'What to search for (functions, patterns, etc.)',
                            },
                            'max_results': {
                                'type': 'integer',
                                'description': 'Maximum number of results to return (default: 10)',
                                'default': 10
                            }
                        },
                        'required': ['codebase_name', 'query'],
                    },
                },
            },
            {
                'type': 'function',
                'function': {
                    'name': 'get_codebase_stats',
                    'description': 'Get statistics and information about a specific codebase',
                    'parameters': {
                        'type': 'object',
                        'properties': {
                            'codebase_name': {
                                'type': 'string',
                                'description': 'Name of the codebase to get stats for',
                            }
                        },
                        'required': ['codebase_name'],
                    },
                },
            }
        ]
    
    def execute_tool(self, tool_name, arguments):
        """Execute a tool call by making API request to code analyzer"""
        try:
            if tool_name == 'list_codebases':
                # Use the working endpoint that actually returns codebases
                response = requests.get(
                    f"{self.code_analyzer_url}/codebases",
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    # Handle the actual response format: {"codebases": [...], "total": 11, ...}
                    if isinstance(data, dict) and 'codebases' in data:
                        codebase_list = data['codebases']
                        codebases = [cb['name'] for cb in codebase_list if isinstance(cb, dict) and 'name' in cb]
                        return {"codebases": codebases}
                    else:
                        # Fallback for unexpected format
                        codebases = [cb['name'] for cb in data if isinstance(cb, dict) and 'name' in cb]
                        return {"codebases": codebases}
                else:
                    return {"error": f"List codebases failed: {response.status_code}"}

            elif tool_name == 'query_codebase':
                # Use the search endpoint which should work
                response = requests.post(
                    f"{self.code_analyzer_url}/search",
                    json={
                        "query": arguments.get('query', ''),
                        "codebase_name": arguments.get('codebase_name', ''),
                        "n_results": arguments.get('max_results', 10)
                    },
                    timeout=60
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Query codebase failed: {response.status_code} - {response.text}"}

            elif tool_name == 'get_codebase_stats':
                # Try to get stats from the codebases endpoint
                response = requests.get(
                    f"{self.code_analyzer_url}/codebases",
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    codebase_name = arguments.get('codebase_name', '')

                    # Find the specific codebase
                    for cb in data:
                        if isinstance(cb, dict) and cb.get('name') == codebase_name:
                            return {"codebase": cb}

                    return {"error": f"Codebase '{codebase_name}' not found"}
                else:
                    return {"error": f"Get codebase stats failed: {response.status_code}"}
            else:
                return {"error": f"Unknown tool: {tool_name}"}

        except Exception as e:
            return {"error": f"Tool execution exception: {str(e)}"}
    
    def chat_with_tools(self, user_message, model="llama3.1:latest"):
        """Chat with model using native Ollama tool calling"""
        tools = self.create_code_analyzer_tools()
        
        messages = [
            {'role': 'user', 'content': user_message}
        ]
        
        # Initial request with tools
        payload = {
            'model': model,
            'messages': messages,
            'tools': tools,
            'stream': False
        }
        
        try:
            response = requests.post(
                f"{self.ollama_url}/api/chat",
                json=payload,
                timeout=120
            )
            
            if response.status_code != 200:
                return f"Error: {response.status_code} - {response.text}"
            
            data = response.json()
            message = data.get('message', {})
            tool_calls = message.get('tool_calls', [])
            content = message.get('content', '')
            
            # If no tool calls, return the content
            if not tool_calls:
                return content
            
            # Execute tool calls
            messages.append(message)  # Add assistant's response with tool calls
            
            for tool_call in tool_calls:
                function = tool_call.get('function', {})
                tool_name = function.get('name', '')
                arguments_str = function.get('arguments', '{}')
                
                # Parse arguments
                try:
                    arguments = json.loads(arguments_str)
                except:
                    arguments = {}
                
                # Execute the tool
                tool_result = self.execute_tool(tool_name, arguments)
                
                # Add tool result to messages
                messages.append({
                    'role': 'tool',
                    'content': json.dumps(tool_result)
                })
            
            # Get final response from model
            final_payload = {
                'model': model,
                'messages': messages,
                'stream': False
            }
            
            final_response = requests.post(
                f"{self.ollama_url}/api/chat",
                json=final_payload,
                timeout=120
            )
            
            if final_response.status_code == 200:
                final_data = final_response.json()
                return final_data.get('message', {}).get('content', 'No response')
            else:
                return f"Final response error: {final_response.status_code}"
                
        except Exception as e:
            return f"Exception: {str(e)}"

def test_direct_ollama_interface():
    """Test the direct Ollama interface"""
    print("🧪 Testing Direct Ollama Code Analyzer Interface")
    print("=" * 60)
    
    analyzer = DirectOllamaCodeAnalyzer()
    
    test_queries = [
        "List all available codebases",
        "Find memory management functions in the utils codebase",
        "What are the statistics for the z80emu codebase?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}/{len(test_queries)}] Testing: '{query}'")
        print("-" * 40)
        
        result = analyzer.chat_with_tools(query)
        print(f"Result: {result[:300]}...")
        
        if "error" not in result.lower():
            print("✅ Success!")
        else:
            print("❌ Error occurred")

def main():
    print("🚀 Direct Ollama Code Analysis Interface")
    print("=" * 60)
    print("This bypasses OpenWebUI and uses native Ollama tool calling")
    
    test_direct_ollama_interface()
    
    print(f"\n💡 Usage:")
    print("analyzer = DirectOllamaCodeAnalyzer('http://************:11434')")
    print("result = analyzer.chat_with_tools('List codebases')")

if __name__ == "__main__":
    main()
