{"TypeScript-Node-Starter-master": {"analysis": {"name": "TypeScript-Node-Starter-master", "languages": ["TypeScript", "JavaScript"], "file_types": {".example": 1, "": 4, ".yml": 2, ".ts": 20, ".js": 3, ".json": 7, ".md": 1, ".scss": 131, ".eot": 2, ".svg": 2, ".ttf": 2, ".woff": 2, ".woff2": 2, ".otf": 1, ".png": 1, ".pug": 13}, "key_files": ["package.json", "README.md"], "functions": ["isAuthorized", "isAuthenticated", "getApi", "getFacebook", "getContact", "index", "sendResetPasswordEmail", "resetPassword", "createRandomToken", "sendForgotPasswordEmail", "done", "setRandomToken", "comparePasswordFunction", "gravatar", "save", "extendAccessToken", "setOptions", "get", "getOauthUrl", "setAppSecret", "post", "getOptions", "getGraphUrl", "setVersion", "setGraphUrl", "fql", "getAccessToken", "search", "setAccessToken", "del", "batch", "authorize", "getAppSecret"], "classes": ["AuthToken", "Request", "Flash", "SessionData"], "modules": [], "keywords": ["trace", "debug", "http", "hash", "log", "server", "connection", "option", "handle", "error", "delay", "lock", "parameter", "crypto", "async", "event", "memory", "setting", "schedule", "timer", "client", "timeout", "serial", "protocol", "exception", "query", "sync", "config"], "total_files": 194, "total_lines": 1147}, "questions": ["Find memory management functions in TypeScript-Node-Starter-master", "Find error handling patterns in TypeScript-Node-Starter-master", "Locate configuration management in TypeScript-Node-Starter-master", "Find timer and timeout handling in TypeScript-Node-Starter-master", "Show security and encryption code in TypeScript-Node-Starter-master"]}, "bookstore": {"analysis": {"name": "bookstore", "languages": ["TypeScript", "Prisma"], "file_types": {".ts": 4, ".prisma": 1}, "key_files": [], "functions": ["startServer"], "classes": ["Order", "OrderModel", "UserModel", "User", "OrderModel", "UserModel"], "modules": [], "keywords": ["client", "http", "log", "async", "server", "sync"], "total_files": 5, "total_lines": 280}, "questions": ["Show concurrency and threading code in bookstore", "Show TypeScript interfaces and types in bookstore", "Find async/await patterns in bookstore", "Explain the startServer function in bookstore", "Show the Order class implementation in bookstore"]}, "go-example-master": {"analysis": {"name": "go-example-master", "languages": ["JavaScript", "Go"], "file_types": {".mod": 7, ".sum": 4, "": 4, ".md": 7, ".go": 37, ".yaml": 1, ".ico": 1, ".html": 1, ".js": 1, ".css": 1, ".sh": 4, ".yml": 1, ".tmpl": 2}, "key_files": ["go.mod", "README.md", "README.md", "README.md", "go.mod", "go.mod", "go.mod", "README.md", "go.mod", "go.mod", "go.mod", "README.md"], "functions": ["helloH<PERSON>ler", "init", "fetchMessage", "PrintDefsUses", "main", "main", "main", "main", "PrintHugeParams", "main", "main", "CheckNilFuncComparison", "main", "main", "main", "isValidIdentifier", "PrintSkeleton", "nodeString", "mode", "main", "main", "usage", "ExampleString", "String", "TestString", "version", "greet", "main", "usage", "cleanListing", "main", "include", "leadingTabs", "isTagged", "main", "NewServer", "TestIntegration", "TestIsTagged", "renderJSON", "readRequestJSON", "main", "decodeGetResults", "combinedWeaviateError", "initWeaviate", "renderJSON", "readRequestJSON", "main", "renderJSON", "readRequestJSON", "main", "New", "Test", "New", "Test", "TestSlogtest", "parseLogEntries", "parseGroup", "TestParseLogEntries", "New", "Test", "TestSlogtest", "TestParseLogEntries", "parseLogEntries", "New", "freeBuf", "allocBuf", "TestAlloc", "Test", "TestSlogtest", "TestParseLogEntries", "parseLogEntries", "imageHandler", "main", "indexHandler"], "classes": ["A", "my<PERSON><PERSON><PERSON>", "buffer", "Server", "addRequest", "queryRequest", "ragServer", "document", "addRequest", "queryRequest", "ragServer", "document", "addRequest", "queryRequest", "ragServer", "document", "Options", "Indent<PERSON><PERSON><PERSON>", "Options", "groupOrAttrs", "Indent<PERSON><PERSON><PERSON>", "Options", "Indent<PERSON><PERSON><PERSON>", "Options", "Indent<PERSON><PERSON><PERSON>", "Link", "Image", "Index"], "modules": [], "keywords": ["debug", "http", "flag", "log", "server", "pool", "setting", "alloc", "option", "client", "handle", "exception", "error", "mutex", "query", "sync", "lock", "buffer", "config", "parameter", "free"], "total_files": 71, "total_lines": 3525}, "questions": ["Find memory management functions in go-example-master", "Find error handling patterns in go-example-master", "Locate configuration management in go-example-master", "Show TypeScript interfaces and types in go-example-master", "Find async/await patterns in go-example-master"]}, "modbus": {"analysis": {"name": "modbus", "languages": ["C"], "file_types": {".c": 18, ".h": 24}, "key_files": [], "functions": ["mbalink_parseBytes", "mblink_findSession", "mbalink_initConfig", "memcpy", "mbalink_getNeededBytes", "pUserRxFrameCallback", "mbalink_transmitFrame", "tmwtarg_getMSTime", "pParseFunc", "MBDIAG_LINK_FRAME_RECEIVED", "MBDIAG_LINK_FRAME_SENT", "while", "TMWDIAG_ERROR", "MBDEFS_LOCHAR", "for", "return", "pPhysTransmit", "TMWCHNL_STAT_CALLBACK_FUNC", "TMWTARG_UNUSED_PARAM", "MBDEFS_HICHAR", "mbalink_parseBytes", "mbalink_getNeededBytes", "mbalink_initConfig", "mbalink_transmitFrame", "tmwappl_setInitialized", "tmwchnl_initChannel", "tmwphys_getChannelConfig", "mbchnl_sendMessage", "tmwtarg_stopThreads", "mblink_initConfig", "MBDIAG_FRAME_SENT", "mbchnl_closeChannel", "_sendNextFrame", "pLinkGetSessions", "tmwmem_alloc", "tmwdlist_getNext", "mbmem_alloc", "tmwmem_free", "mbchnl_discardInvLen", "mbchnl_modifyLink", "mbchnl_afterTxCallback", "mbchnl_setChannelConfig", "TMWTARG_UNLOCK_SECTION", "tmwchnl_deleteChannel", "mbchnl_setChannelConfig", "mbchnl_afterTxCallback", "mbchnl_sendMessage", "mbchnl_openChannel", "mbchnl_modifyPhys", "mbchnl_initConfig", "mbchnl_closeChannel", "mbchnl_cleanupMessage", "mbchnl_freeTxData", "mbchnl_newTxData", "mbchnl_failedTxCallback", "mbchnl_modifyLink", "mbchnl_deleteMessages", "mbchnl_removeRequest", "mbchnl_discardInvLen", "mbchnl_getChannelConfig", "mbdiag_showDiagsResponse", "MBDEFS_MAKEWORD", "mbdiag_response_error", "memcpy", "mbdiag_showDeviceId", "_getResponseErrorMsg", "_displayBytes", "_fcString", "_displayLinkFrame", "switch", "mbdiag_frameSent", "while", "_displayLINKargs", "mbdiag_frameReceived", "mbdiag_removeMessage", "mbdiag_linkFrameReceived", "mbdiag_showExceptionStatus", "_displayFCargs", "for", "tmwdiag_skipLine", "mbdiag_frameReceived", "mbdiag_showDiagsResponse", "mbdiag_error", "mbdiag_insertQueue", "mbdiag_showCoil", "mbdiag_removeMessage", "mbdiag_linkFrameReceived", "mbdiag_showExceptionStatus", "mbdiag_response_error", "mbdiag_linkFrameSent", "mbdiag_frameSent", "mbdiag_showDeviceIdData", "mbdiag_showDiscreteInput", "mbdiag_showDeviceId", "mbdiag_showInputRegister", "TMWTARG_UNUSED_PARAM", "mbdiag_showHoldingRegister", "_cancelFrame", "mblink_initConfig", "mblink_monitorModeFindSession", "pInfoFunc", "_openSession", "mblink_findSession", "mbalink_initConfig", "mbmem_alloc", "mbmem_free", "mbtlink_initConfig", "mblink_registerCallback", "switch", "tmwtimer_init", "mbrlink_initConfig", "tmwlink_initChannel", "tmwlink_openChannel", "mbplink_initConfig", "mblink_modifyChannel", "return", "tmwtimer_cancel", "mblink_initChannel", "mblink_initConfig", "mblink_monitorModeFindSession", "mblink_findSession", "mblink_modifyChannel", "mblink_registerCallback", "mblink_deleteChannel", "mbmem_free", "mbmem_init", "TMWMEM_GETTYPE", "TMWMEM_GETHEADER", "return", "tmwmem_lowFree", "mbmem_getUsage", "mbmem_alloc", "mbmem_initConfig", "mbmem_free", "mbmem_init", "mbmem_getUsage", "mbmem_alloc", "mbmem_initConfig", "mbplink_initConfig", "mbplink_parseBytes", "tmwtarg_getMSTime", "pParseFunc", "MBDIAG_ERROR", "mblink_findSession", "tmwsesn_setOnline", "MBDIAG_LINK_FRAME_RECEIVED", "return", "memcpy", "MBDIAG_LINK_FRAME_SENT", "mbplink_getNeededBytes", "pUserRxFrameCallback", "TMWDIAG_ERROR", "pPhysTransmit", "TMWCHNL_STAT_CALLBACK_FUNC", "TMWTARG_UNUSED_PARAM", "mbplink_transmitFrame", "mbplink_initConfig", "mbplink_parseBytes", "mbplink_getNeededBytes", "mbplink_transmitFrame", "mblink_findSession", "MBDEFS_LOBYTE", "memcpy", "pUserRxFrameCallback", "tmwtarg_getMSTime", "pParseFunc", "MBDIAG_LINK_FRAME_RECEIVED", "MBDIAG_LINK_FRAME_SENT", "while", "mbrlink_initConfig", "TMWDIAG_ERROR", "mbrlink_parseBytes", "for", "return", "pPhysTransmit", "TMWCHNL_STAT_CALLBACK_FUNC", "TMWTARG_UNUSED_PARAM", "mbrlink_transmitFrame", "MBDIAG_ERROR", "computeCRC16", "mbrlink_getNeededBytes", "mbrlink_transmitFrame", "mbrlink_initConfig", "mbrlink_parseBytes", "return", "mbsesn_closeSession", "tmwsesn_openSession", "mbsesn_openSession", "mbsesn_closeSession", "mbsesn_openSession", "mbtlink_parseBytes", "MBDEFS_MAKEWORD", "_rxFrameTimeout", "_msgReceived", "MBDEFS_LOBYTE", "memcpy", "pUserRxFrameCallback", "tmwdlist_getNext", "mbtlink_transmitFrame", "tmwtarg_getMSTime", "mbtlink_initConfig", "pParseFunc", "MBDIAG_LINK_FRAME_RECEIVED", "MBDIAG_LINK_FRAME_SENT", "while", "TMWDIAG_ERROR", "_findSession", "_findRequest", "return", "tmwtimer_cancel", "mbtlink_parseBytes", "mbtlink_getNeededBytes", "mbtlink_transmitFrame", "mbtlink_initConfig", "_initTxData", "MBDEFS_LOBYTE", "memcpy", "mmbbrm_readHoldingRegisters", "mmbbrm_writeMultipleRegisters", "mmbbrm_readDeviceId", "mmbbrm_readExceptionStatus", "mmbbrm_sendCustomPdu", "mmbbrm_writeSingleRegister", "mmbbrm_diagnostics", "for", "return", "mmbbrm_writeMultipleCoils", "mbchnl_newTxData", "MBDIAG_ERROR", "mmbbrm_initReqDesc", "mmbbrm_writeSingleCoil", "mmbbrm_readInputRegisters", "mmbbrm_readCoils", "mmbbrm_readDiscreteInputs", "mmbbrm_readDeviceId", "mmbbrm_initReqDesc", "mmbbrm_writeSingleCoil", "mmbbrm_readInputRegisters", "mmbbrm_readExceptionStatus", "mmbbrm_diagnostics", "mmbbrm_readCoils", "mmbbrm_readDiscreteInputs", "mmbbrm_readHoldingRegisters", "mmbbrm_writeMultipleCoils", "mmbbrm_writeMultipleRegisters", "mmbbrm_initBroadcastDesc", "mmbbrm_ReadWriteMultipleRegisters", "mmbbrm_sendCustomPdu", "mmbbrm_maskWriteRegister", "mmbbrm_writeSingleRegister", "mmbdata_storeInputRegisters", "mmbdata_init", "mmbdata_close", "mmbsim_close", "mmbdata_storeDiscreteInputs", "mmbdata_storeHoldingRegisters", "mmbdata_storeDiagnosticResponse", "return", "TMWDIAG_ERROR", "mmbdata_storeCoils", "mmbdata_storeExceptionStatus", "mmbdata_storeDeviceId", "TMWTARG_UNUSED_PARAM", "MMBDatabaseWrapper_Close", "mmbdata_storeInputRegisters", "mmbdata_init", "mmbdata_close", "mmbdata_storeDiscreteInputs", "mmbdata_storeHoldingRegisters", "mmbdata_storeDiagnosticResponse", "mmbdata_storeCoils", "mmbdata_storeExceptionStatus", "mmbdata_storeDeviceId", "tmwappl_setInitialized", "mmbmem_initMemory", "mmbmem_getUsage", "_initConfig", "TMWMEM_GETTYPE", "mmbmem_alloc", "TMWMEM_GETHEADER", "tmwmem_lowFree", "mmbmem_free", "return", "mmbmem_initConfig", "mmbmem_init", "tmwmem_initConfig", "mbmem_initConfig", "mmbmem_initMemory", "mmbmem_getUsage", "mmbmem_alloc", "mmbmem_free", "mmbmem_initConfig", "mmbmem_init", "tmwappl_setInitialized", "mmbsesn_closeSession", "MBDEFS_MAKEWORD", "_processReadDeviceId", "_processReadDiscreteInputs", "_processReadExceptionStatus", "_processReadCoils", "mmbdata_storeCoils", "mbsesn_closeSession", "mbchnl_discardInvLen", "TMWTARG_UNLOCK_SECTION", "_processWriteSingleRegister", "switch", "mmbsesn_initConfig", "mmbmem_alloc", "mmbsesn_modifySession", "_processReadWriteMultipleRegisters", "_processFrame", "TMWTARG_LOCK_SECTION", "mmbmem_free", "mmbsesn_closeSession", "mmbsesn_setSessionConfig", "mmbsesn_initConfig", "mmbsesn_modifySession", "mmbsesn_getSessionConfig", "mmbsesn_openSession", "tmwsim_tableCreate", "mmbsim_storeHoldingRegisters", "mmbsim_exceptionStatusRead", "mmbsim_inputRegisterRead", "pUpdateCallback", "mmbsim_deleteDeviceId", "mmbsim_addCoil", "tmwtarg_appendString", "tmwsim_tableGetNextPoint", "mmbsim_discreteInputRead", "mmbsim_coilRead", "mmbsim_addDiscreteInput", "tmwsim_initAnalog", "mmbsim_addHoldingRegister", "mmbsim_storeExceptionStatus", "mmbsim_deleteHoldingRegister", "tmwsim_setBinaryValue", "tmwsim_initBinary", "mmbmem_alloc", "mmbsim_storeDeviceId", "mmbsim_storeHoldingRegisters", "mmbsim_exceptionStatusRead", "mmbsim_inputRegisterRead", "mmbsim_deleteDeviceId", "mmbsim_addCoil", "mmbsim_discreteInputRead", "mmbsim_coilRead", "mmbsim_addDiscreteInput", "mmbsim_addHoldingRegister", "mmbsim_storeExceptionStatus", "mmbsim_deleteHoldingRegister", "mmbsim_storeDeviceId", "mmbsim_close", "mmbsim_discreteInputLookupPoint", "mmbsim_deviceIdRead", "mmbsim_deviceIdLookupPoint", "mmbsim_deleteInputRegister", "mmbsim_storeInputRegisters", "mmbsim_init", "mmbsim_saveDatabase", "SMBDatabaseWrapper_diagListenOnly", "SMBDatabaseWrapper_Close", "SMBDatabaseWrapper_diagClearCount", "SMBDatabaseWrapper_getNextDeviceId", "smbdata_diagListenOnly", "smbdata_DiscreteInputsValidateRange", "smbdata_close", "smbdata_diagGetValue", "SMBDatabaseWrapper_diagnostics", "smbdata_storeCoils", "switch", "SMBDatabaseWrapper_readDeviceId", "TMWDIAG_ERROR", "smbdata_diagClearCount", "smbsim_deviceIdGetPoint", "smbdata_storeHoldingRegisters", "smbdata_getNextDeviceId", "return", "smbdata_InputRegistersValidateRange", "smbdata_init", "smbdata_diagListenOnly", "smbdata_DiscreteInputsValidateRange", "smbdata_close", "smbdata_diagGetValue", "smbdata_storeCoils", "smbdata_diagClearCount", "smbdata_storeHoldingRegisters", "smbdata_getNextDeviceId", "smbdata_InputRegistersValidateRange", "smbdata_init", "smbdata_readExceptionStatus", "smbdata_getInputRegisters", "smbdata_diagSubFunctionSupported", "smbdata_getCoils", "smbdata_diagRestart", "smbdata_readDeviceId", "smbdata_getHoldingRegisters", "smbdata_diagnostics", "smbdata_getDiscreteInputs", "smbdata_CoilsValidateRange", "tmwappl_setInitialized", "smbmem_initConfig", "smbmem_free", "_initConfig", "TMWMEM_GETTYPE", "TMWMEM_GETHEADER", "return", "smbmem_init", "smbmem_alloc", "smbmem_getUsage", "tmwmem_lowFree", "smbmem_initMemory", "tmwmem_initConfig", "mbmem_initConfig", "smbmem_initConfig", "smbmem_free", "smbmem_init", "smbmem_alloc", "smbmem_getUsage", "smbmem_initMemory", "tmwappl_setInitialized", "smbsesn_processFrame", "MBDEFS_MAKEWORD", "MBDEFS_LOBYTE", "_processReadDeviceId", "memcpy", "_processReadDiscreteInputs", "smbsesn_setSessionConfig", "_sendResponseFailure", "_processReadExceptionStatus", "_processReadCoils", "MBDIAG_RESPONSE_ERROR", "smbdata_diagListenOnly", "_sendResponse", "mbsesn_closeSession", "smbsesn_initConfig", "TMWTARG_UNLOCK_SECTION", "mbchnl_discardInvLen", "smbdata_diagGetValue", "smbdata_close", "smbsesn_modifySession", "smbsesn_closeSession", "smbsesn_openSession", "smbsesn_setSessionConfig", "smbsesn_getSessionConfig", "smbsesn_initConfig", "smbsesn_processFrame", "smbsesn_sendResponse", "smbsim_clear", "pUpdateCallback", "tmwsim_tableFindPoint", "smbsim_dInputGetLastPoint", "smbsim_dInputRead", "smbsim_DiscreteInputsValidateRange", "smbsim_iRegGetLastPoint", "tmwsim_initBinary", "_validate<PERSON>ange", "smbsim_hRegGetPointByIndex", "tmwsim_getAnalogValue", "smbsim_iRegWrite", "smbsim_hRegBlockRead", "tmwsim_tableDestroy", "smbsim_hRegWrite", "_buildDb", "smbsim_addDiscreteInput", "smbsim_deviceIdGetLastPoint", "TMWTARG_UNUSED_PARAM", "smbsim_deleteHoldingRegister", "smbsim_init", "smbs<PERSON>_setCallback", "smbsim_clear", "smbsim_addHoldingRegister", "smbsim_coilGetPointByIndex", "smbsim_isPointEnabled", "smbsim_deleteDeviceId", "smbsim_hRegQuantity", "smbsim_saveDatabase", "smbsim_dInputGetLastPoint", "smbsim_CoilBlockWrite", "smbsim_dInputRead", "smbsim_DiscreteInputsValidateRange", "smbsim_enablePoint", "smbsim_deleteCoil", "smbsim_hRegBlockWrite", "smbsim_iRegGetLastPoint", "smbsim_HoldingRegistersValidateRange", "smbsim_readDeviceId", "smbsim_hRegRead"], "classes": [], "modules": [], "keywords": ["http", "flag", "log", "server", "connection", "channel", "tcp", "handle", "error", "lock", "database", "udp", "thread", "parameter", "malloc", "communication", "async", "event", "modbus", "memory", "pool", "timer", "alloc", "setting", "timeout", "client", "serial", "protocol", "exception", "query", "sync", "transaction", "buffer", "config", "free"], "total_files": 42, "total_lines": 14601}, "questions": ["Find memory management functions in modbus", "Show network communication code in modbus", "Find error handling patterns in modbus", "Locate configuration management in modbus", "Find timer and timeout handling in modbus"]}, "networking_project": {"analysis": {"name": "networking_project", "languages": ["C", "C++"], "file_types": {".h": 1, ".cpp": 1}, "key_files": [], "functions": ["BufferPool", "releaseBuffer", "acquireBuffer", "preallocateBuffers", "malloc", "close", "memset", "bindAndListen", "htons", "allocate<PERSON><PERSON><PERSON>", "socket", "is_connected", "deallocate<PERSON><PERSON><PERSON>", "SocketManager", "runtime_error", "cleanup", "free", "bad_alloc"], "classes": ["SocketManager", "sockaddr", "sockaddr_in"], "modules": [], "keywords": ["malloc", "log", "connection", "memory", "pool", "tcp", "alloc", "error", "mutex", "socket", "buffer", "thread", "free"], "total_files": 2, "total_lines": 93}, "questions": ["Find memory management functions in networking_project", "Show network communication code in networking_project", "Find error handling patterns in networking_project", "Show concurrency and threading code in networking_project", "Show C/C++ data structures in networking_project"]}, "rust-starter-master": {"analysis": {"name": "rust-starter-master", "languages": ["Rust"], "file_types": {"": 1, ".toml": 2, ".md": 1, ".rs": 13}, "key_files": ["Cargo.toml", "README.md"], "functions": ["foo", "test_macro_works_at_all", "main", "doit", "doit", "doit", "doit"], "classes": ["Bar"], "modules": [], "keywords": [], "total_files": 17, "total_lines": 81}, "questions": ["Show Rust ownership patterns in rust-starter-master", "Find error handling with Result types in rust-starter-master", "Explain the foo function in rust-starter-master", "Compare foo and test_macro_works_at_all functions in rust-starter-master", "Show the Bar class implementation in rust-starter-master"]}, "test_project": {"analysis": {"name": "test_project", "languages": ["C++"], "file_types": {".cpp": 2}, "key_files": ["main.cpp"], "functions": ["test", "name_", "greet", "main", "formatString"], "classes": ["TestClass"], "modules": [], "keywords": [], "total_files": 2, "total_lines": 32}, "questions": ["Show C/C++ data structures in test_project", "Find pointer usage patterns in test_project", "Explain the test function in test_project", "Compare test and name_ functions in test_project", "Show the TestClass class implementation in test_project"]}, "utils": {"analysis": {"name": "utils", "languages": ["C", "C++", "C#"], "file_types": {".cpp": 3, ".h": 27, ".c": 19, ".cs": 1}, "key_files": [], "functions": ["tmwsim_tableCreate", "tmwsim_tableGetNextPoint", "tmwsim_tableFindPoint", "tmwtree_successor", "TMWTARG_UNLOCK_SECTION", "tmwsim_tableSize", "tmwsim_tableFindPointByIndex", "TMWTARG_LOCK_SECTION", "InfoDest", "tmwsim_deletePoint", "tmwtree_create", "tmwtree_getLast", "tmwtree_destroy", "tmwtree_exactQuery", "tmwsim_tableDestroy", "for", "return", "tmwsim_tableGetLastPoint", "TMWTARG_LOCK_DELETE", "tmwsim_tableGetFirstPoint", "tmwappl_initSCL", "tmwappl_setInitialized", "tmwmem_alloc", "tmwappl_checkForInput", "tmwmem_free", "TMWTARG_UNLOCK_SECTION", "tmwappl_init", "TMWTARG_LOCK_SECTION", "tmwmem_close", "while", "tmwappl_initApplication", "tmwdlist_initialize", "tmwappl_findChannel", "return", "tmwappl_closeApplication", "tmwappl_getInitialized", "TMWTARG_UNUSED_PARAM", "TMWTARG_LOCK_DELETE", "tmwtimer_applInit", "pPhysReceive", "tmwappl_initSCL", "tmwappl_init", "tmwappl_setInitialized", "tmwappl_initApplication", "tmwappl_findChannel", "tmwappl_internalInit", "tmwappl_closeApplication", "tmwappl_checkForInput", "tmwappl_getInitialized", "tmwchnl_initChannel", "tmwchnl_getChannelName", "tmwchnl_unlockChannel", "TMWTARG_UNLOCK_SECTION", "tmwchnl_deleteChannel", "tmwchnl_setStatCallback", "tmwchnl_callStatCallback", "tmwchnl_setUserDataPtr", "tmwchnl_setIdleCallback", "TMWTARG_LOCK_SECTION", "tmwchnl_lockChannel", "tmwtimer_init", "tmwchnl_getUserDataPtr", "tmwdlist_initialize", "return", "TMWTARG_UNUSED_PARAM", "pStatCallbackFunc", "TMWTARG_LOCK_DELETE", "tmwtimer_initMultiTimer", "TMWTARG_LOCK_INIT", "tmwchnl_initChannel", "tmwchnl_deleteChannel", "tmwchnl_getChannelName", "tmwchnl_setStatCallback", "tmwchnl_callStatCallback", "tmwchnl_setUserDataPtr", "tmwchnl_setIdleCallback", "mdnpsesn_openSession", "mdnpsesn_initConfig", "sdnpsesn_initConfig", "sdnpsesn_openSession", "tmwchnl_lockChannel", "tmwchnl_unlockChannel", "tmwchnl_getUserDataPtr", "TMWTARG_UNUSED_PARAM", "tmwcrypto_getAsymKeyTypeSize", "ASN1_STRING_length", "EVP_EncryptUpdate", "tmwcrypto_algorithmSupport", "AES_unwrap_key", "apps_startup", "tmwcrypto_commitKey", "X509_EXTENSION_get_data", "X509_get_notBefore", "_testCrypto", "tmwcrypto_setSessionKeyData", "OpenSSL_add_all_algorithms", "RAND_bytes", "tmwcrypto_generateNewKey", "switch", "X509_get_pubkey", "TMWTARG_LOCK_SECTION", "X509_free", "EVP_SignFinal", "EVP_PKEY_id", "tmwcrypto_getRandomData", "tmwcrypto_getAsymKeyTypeSize", "tmwcrypto_algorithmSupport", "tmwcrypto_verifyUserCertificate", "tmwcrypto_commitKey", "tmwcrypto_setSessionKeyData", "tmwcrypto_verifySignature", "tmwcrypto_close", "tmwcrypto_generateNewKey", "tmwcrypto_init", "tmwcrypto_getKeyData", "tmwcrypto_verifyCertificate", "tmwcrypto_decryptData", "tmwcrypto_configSimKey", "tmwcrypto_MACValue", "tmwcrypto_getAlgorithm", "tmwcrypto_encryptData", "tmwcrypto_putCertificate", "tmwcrypto_genDigitalSignature", "tmwcrypto_getCertificate", "tmwdb_storeEntry", "tmwdb_setMaxSize", "tmwdb_destroy", "TMWTARG_UNLOCK_SECTION", "tmwdb_unlockQueue", "tmwtarg_free", "TMWTARG_LOCK_SECTION", "tmwdb_init", "while", "TMWDIAG_ERROR", "tmwdlist_size", "tmwdb_lockQueue", "tmwdlist_initialize", "return", "tmwdb_closeDatabase", "tmwdb_getSize", "TMWTARG_UNUSED_PARAM", "TMWTARG_LOCK_DELETE", "tmwdb_addEntry", "tmwdlist_getFirst", "tmwdb_unlockQueue", "tmwdb_storeEntry", "tmwdb_addEntry", "tmwdb_initData", "tmwdb_closeDatabase", "tmwdb_init", "tmwdb_setMaxSize", "tmwdb_getSize", "tmwdb_lockQueue", "tmwdb_destroy", "tmwdiag_message", "tmwchnl_getChannelName", "tmwtarg_snprintf", "tmwdiag_time2string", "switch", "pDiagChkFilterCallback", "tmwsctr_getSectorName", "tmwtarg_putDiagString", "tmwdiag_skipLine", "tmwdiag_warning", "tmwdiag_error", "tmwdiag_message_name", "tmwdiag_showMemoryUsage", "tmwdiag_initId", "tmwdiag_checkFilter", "tmwdtime_getDateTime", "tmwdiag_target", "tmwdiag_putLine", "tmwsesn_getSessionName", "tmwdiag_setChkFilterCallback", "tmwdiag_time2string", "tmwdiag_message", "pSector", "tmwdiag_target", "tmwdiag_putLine", "tmwdiag_skipLine", "tmwdiag_warning", "tmwdiag_error", "tmwdiag_message_name", "tmwdiag_showMemoryUsage", "tmwdiag_setChkFilterCallback", "tmwdiag_initId", "tmwdiag_checkFilter", "TMWTARG_UNUSED_PARAM", "tmwdlist_addEntry", "tmwdlist_destroy", "tmwdlist_removeFirst", "tmwdlist_containsEntry", "ASSERT", "tmwdlist_getFirst", "tmwdlist_initialize", "return", "tmwdlist_getEntry", "tmwdlist_insertEntryAfter", "pFreeFunc", "tmwdlist_insertEntryAt", "tmwdlist_insertEntryBefore", "tmwdlist_removeEntry", "tmwdlist_size", "tmwdlist_getNext", "tmwdlist_getAfter", "tmwdlist_getLast", "tmwdlist_addEntry", "tmwdlist_destroy", "tmwdlist_removeFirst", "tmwdlist_containsEntry", "tmwdlist_getFirst", "tmwdlist_initialize", "tmwdlist_getEntry", "tmwdlist_insertEntryAfter", "tmwdlist_insertEntryAt", "tmwdlist_insertEntryBefore", "tmwdlist_removeEntry", "tmwdlist_size", "tmwdlist_getNext", "tmwdlist_getAfter", "tmwdlist_getLast", "tmwdtime_addMinutes", "tmwdtime_decrementDay", "tmwtarg_getDateTime", "for", "return", "tmwdtime_subtractOffset", "while", "tmwdtime_decrementHour", "tmwdtime_addOffset", "tmwdtime_incrementDay", "_incrementMinute", "tmwdtime_computeDayOfYear", "tmwdtime_subtractMinutes", "_decrementMinute", "tmwdtime_getDateTime", "tmwdtime_incrementHour", "tmwdtime_addMinutes", "tmwdtime_compareTime", "tmwdtime_computeDayOfWeek", "tmwdtime_checkTimeOrder", "tmwdtime_hours24ToAmPm", "tmwdtime_subtractOffset", "tmwdtime_decrementHour", "tmwdtime_addOffset", "tmwdtime_incrementDay", "tmwdtime_decrementDay", "tmwdtime_hoursAmPmTo24", "tmwdtime_computeDayOfYear", "tmwdtime_subtractMinutes", "tmwdtime_adjustMinutes", "tmwdtime_getDateTime", "tmwdtime_incrementHour", "pLinkInfoFunc", "TMWPHYSD_ERROR", "tmwlink_closeChannel", "tmwlink_closeSession", "tmwlink_setCallbacks", "tmwlink_channelCallback", "tmwlink_getSessions", "tmwtimer_init", "tmwlink_setChannelInactive", "tmwlink_checkOpen", "tmwdlist_size", "tmwlink_initChannel", "_openChannelTimeout", "tmwlink_openChannel", "pPhysCloseChannel", "tmwdlist_initialize", "tmwlink_deleteChannel", "pPhysSetCallbacks", "return", "tmwtimer_cancel", "tmwlink_openChannel", "tmwlink_closeSession", "tmwlink_setCallbacks", "tmwlink_channelCallback", "tmwlink_getSessions", "tmwlink_deleteChannel", "tmwlink_setChannelInactive", "tmwlink_checkOpen", "tmwlink_openSession", "tmwlink_closeChannel", "tmwlink_initChannel", "tmwdlist_removeFirst", "tmwmem_checkLimit", "TMWMEM_GETBUF", "tmwmem_alloc", "tmwmem_lowAlloc", "tmwmem_init", "tmwmem_free", "TMWTARG_UNLOCK_SECTION", "tmwmem_lowInit", "tmwtarg_free", "TMWMEM_GETHEADER", "TMWTARG_LOCK_SECTION", "tmwmem_close", "tmwmem_lowCheckLimit", "tmwmem_initConfig", "for", "tmwdlist_initialize", "return", "tmwmem_lowFree", "tmwtarg_alloc", "tmwmem_lowInit", "tmwmem_getUsage", "tmwmem_checkLimit", "tmwmem_close", "tmwmem_alloc", "tmwmem_lowAlloc", "tmwmem_lowFree", "tmwmem_lowCheckLimit", "tmwmem_initConfig", "tmwmem_init", "tmwmem_free", "tmwmsim_getAnalog", "tmwsim_getBinaryValue", "tmwsim_setFlags", "tmwsim_tableFindPoint", "tmwsim_getIntegratedTotalValue", "tmwsim_setBinaryValue", "tmwmsim_getBitstring", "tmwsim_getStringValue", "tmwmsim_getCounterByIndex", "tmwmsim_setBinary", "tmwsim_tableFindPointByIndex", "tmwsim_setAnalogValue", "tmwmsim_getDoubleBinary", "tmwsim_getAnalogValue", "tmwmsim_getAnalogByIndex", "tmwmsim_setAnalog", "tmwsim_getDoubleBinaryValue", "tmwmsim_getString", "tmwmsim_getBinaryByIndex", "tmwsim_setBitstringTime", "tmwmsim_getAnalog", "tmwmsim_getCounter", "tmwmsim_setAnalog", "tmwmsim_getBitstring", "tmwmsim_getString", "tmwmsim_getCounterByIndex", "tmwmsim_getBinaryByIndex", "tmwmsim_setBinary", "tmwmsim_getIntegratedTotal", "tmwmsim_setBitstring", "tmwmsim_getDoubleBinary", "tmwmsim_getBinary", "tmwmsim_getStringByIndex", "tmwmsim_getAnalogByIndex", "tmwphys_getChannelConfig", "tmwtarg_stopThreads", "_checkA<PERSON>ressCallback", "TMWPHYSD_ERROR", "tmwtarg_receive", "tmwtarg_deleteChannel", "tmwmem_alloc", "_firstCharTimeout", "pNeededCharsFunc", "tmwphys_initChannel", "tmwphys_setChannelConfig", "_channelCallback", "TMWTARG_UNLOCK_SECTION", "tmwmem_free", "tmwtarg_initChannel", "_setCallbacks", "pParseFunc", "_receiveBytes", "tmwtarg_getTransmitReady", "tmwtarg_free", "tmwphys_getChannelConfig", "tmwphys_modifyChannel", "tmwphys_deleteChannel", "tmwphys_initConfig", "tmwphys_modifyPhys", "tmwphys_initChannel", "tmwphys_setChannelConfig", "tmwphysd_channelClosed", "tmwphysd_bytesReceived", "tmwphysd_channelOpened", "switch", "_reasonToText", "for", "tmwphysd_info", "tmwphysd_bytesSent", "tmwdiag_skipLine", "tmwdiag_putLine", "return", "while", "tmwphysd_error", "tmwphysd_bytesReceived", "tmwphysd_channelClosed", "tmwphysd_channelOpened", "tmwphysd_info", "tmwphysd_bytesSent", "TMWTARG_UNUSED_PARAM", "tmwphysd_error", "tmwtarg_getMSTime", "tmwpltmr_checkTimer", "pMultiTimerCallback", "p<PERSON><PERSON><PERSON>", "tmwpltmr_cancelTimer", "tmwpltmr_startTimer", "tmwpltmr_checkMultiTimer", "tmwpltmr_startTimer", "tmwpltmr_checkMultiTimer", "tmwpltmr_checkTimer", "tmwpltmr_cancelTimer", "switch", "<PERSON><PERSON><PERSON><PERSON>", "tmwsctr_getChannelName", "tmwsctr_setUserDataPtr", "pStatCallbackFunc", "tmwsctr_setStatCallback", "tmwsctr_callStatCallback", "return", "tmwsctr_getSectorName", "tmwsctr_getUserDataPtr", "TMWTARG_UNUSED_PARAM", "tmwsctr_openSector", "tmwsctr_closeSector", "tmwsctr_getChannelName", "tmwsctr_setUserDataPtr", "tmwsctr_setStatCallback", "tmwsctr_callStatCallback", "tmwsctr_getSectorName", "tmwsctr_getUserDataPtr", "TMWTARG_UNUSED_PARAM", "tmwsctr_openSector", "tmwsctr_closeSector", "tmwsesn_closeSession", "tmwsesn_setUserDataPtr", "pStatCallbackFunc", "tmwsesn_callStatCallback", "tmwsesn_setOnline", "tmwsesn_getUserDataPtr", "tmwsesn_getSessionName", "tmwsesn_initTxData", "return", "TMWSESN_STAT_CALLBACK_FUNC", "tmwsesn_setStatCallback", "tmwsesn_openSession", "tmwsesn_getChannelName", "ASSERT", "TMWTARG_UNUSED_PARAM", "tmwsesn_closeSession", "tmwsesn_setUserDataPtr", "tmwsesn_callStatCallback", "tmwsesn_setOnline", "tmwsesn_newTxData", "tmwsesn_getUserDataPtr", "tmwsesn_getSessionName", "tmwsesn_initTxData", "tmwsesn_freeTxData", "tmwsesn_openSession", "tmwsesn_setStatCallback", "TMWTARG_UNUSED_PARAM", "tmwsesn_getChannelName", "tmwsim_setEventClass", "tmwsim_setFlags", "tmwsim_setIntervalUnit", "tmwsim_tableFindPoint", "tmwsim_freezeCounter", "tmwsim_setDoubleBinaryValue", "tmwsim_initBinary", "switch", "tmwsim_deletePoint", "tmwsim_initReference", "tmwsim_getAnalogValue", "tmwsim_setIntegratedTotalValue", "tmwsim_getSelectRequired", "tmwsim_tableDestroy", "tmwsim_getFrozenCounterValue", "tmwsim_getEventClass", "_hasXmlSyntax", "tmwsim_getFlags", "tmwsim_setReason", "tmwdtime_getDateTime", "tmwsim_setEventClass", "tmwsim_setFlags", "tmwsim_setIntervalUnit", "tmwsim_tableFindPoint", "tmwsim_freezeCounter", "tmwsim_setDoubleBinaryValue", "tmwsim_initBinary", "tmwsim_deletePoint", "tmwsim_initReference", "tmwsim_getAnalogValue", "tmwsim_setIntegratedTotalValue", "tmwsim_getSelectRequired", "tmwsim_tableDestroy", "tmwsim_getFrozenCounterValue", "tmwsim_getEventClass", "tmwsim_getFlags", "tmwsim_setReason", "tmwsim_getPointNumber", "tmwsim_getAnalogDeadband", "tmwsim_setTimeFormat", "message", "tmwtarg_stopThreads", "tmwtarg_openChannel", "tmwtarg__lockShare", "strcat", "LinIoTarg_setDateTime", "tmwtarg_appendString", "tmwtarg_deleteChannel", "tmwtarg_snprintf", "tmwtarg_receive", "tmwtarg_initMultiTimer", "tmwtarg_initConfig", "tmwtarg_getSessionName", "tmwtarg_getChannelName", "tmwtarg_getMSTime", "tmwtarg_initChannel", "tmwtarg_get24", "tmwtarg_deleteMultiTimer", "tmwtarg_startTimer", "tmwtarg_storeSFloat", "tmwtargp_requestFailure", "tmwtargp_xmtFailureRequested", "tmwtargp_registerPutDiagStringFunc", "tmwtargp_registerGetDateTimeFunc", "tmwtargp_registerCancelTimerFunc", "tmwtargp_registerSetDateTimeFunc", "tmwtargp_registerStartTimerFunc", "tmwtargp_resetChannel", "tmwtargp_registerGetSectorNameFunc", "tmwtargp_waitForInput", "tmwtargp_Sleep", "tmwtargp_registerGetSessionNameFunc", "tmwtimer_getHighWater", "TMWTARG_UNLOCK_SECTION", "tmwtarg_getMSTime", "tmwtarg_startTimer", "tmwtarg_setMultiTimer", "TMWTARG_LOCK_SECTION", "_checkTimerExpired", "tmwtimer_init", "p<PERSON><PERSON><PERSON>", "tmwtarg_cancelTimer", "_timer<PERSON><PERSON><PERSON>", "tmwtimer_initialize", "tmwdlist_initialize", "return", "tmwtimer_cancel", "TMWTARG_UNUSED_PARAM", "TMWTARG_LOCK_DELETE", "tmwtimer_applInit", "tmwtimer_close", "tmwtimer_initMultiTimer", "tmwtimer_getHighWater", "tmwtimer_applInit", "tmwtimer_close", "tmwtimer_initMultiTimer", "tmwtimer_isActive", "tmwtimer_init", "tmwtimer_cancel", "tmwtimer_initialize", "tmwtimer_start", "tmwtprt_setCallbacks", "tmwtprt_openSession", "tmwtprt_closeSession", "return", "tmwtprt_deleteChannel", "tmwtprt_initChannel", "tmwtprt_getSessions", "pLinkSetCallbacks", "tmwtprt_setCallbacks", "tmwtprt_openSession", "tmwtprt_closeSession", "tmwtprt_deleteChannel", "tmwtprt_initChannel", "tmwtprt_getSessions", "PrintKey", "tmwstk_push", "printf", "tmwtree_successor", "tmwstk_create", "tmwtarg_free", "tmwtree_enumerate", "while", "Assert", "_leftRotate", "tmwtree_getLast", "tmwtree_destroy", "tmwtree_print", "_inorderTreePrint", "tmwtree_predecessor", "tmwtree_exactQuery", "_rightRotate", "PrintInfo", "return", "DestroyInfo", "tmwtree_predecessor", "tmwtree_exactQuery", "tmwtree_insert", "NullFunction", "tmwtree_getFirst", "tmwtree_delete", "tmwtree_destroy", "tmwtree_getLast", "tmwtree_successor", "tmwtree_print", "tmwvrsn_getVersionInfo", "tmwvrsn_getVersionTime", "tmwvrsn_getVersionInfo", "tmwvrsn_getVersionTime"], "classes": ["tmwvrsnVersionInfo"], "modules": [], "keywords": ["debug", "network", "hash", "security", "flag", "log", "server", "connection", "channel", "tcp", "decrypt", "option", "handle", "error", "delay", "lock", "database", "udp", "thread", "parameter", "encrypt", "malloc", "communication", "crypto", "async", "event", "memory", "pool", "modbus", "timer", "alloc", "setting", "timeout", "client", "serial", "protocol", "concurrent", "exception", "query", "socket", "sync", "buffer", "config", "free"], "total_files": 50, "total_lines": 20918}, "questions": ["Find memory management functions in utils", "Show network communication code in utils", "Find error handling patterns in utils", "Locate configuration management in utils", "Find timer and timeout handling in utils"]}, "vga-to-spi-tft": {"analysis": {"name": "vga-to-spi-tft", "languages": ["Verilog"], "file_types": {".v": 2}, "key_files": [], "functions": [], "classes": [], "modules": [], "keywords": [], "total_files": 2, "total_lines": 0}, "questions": ["Show the main entry point in vga-to-spi-tft", "Find initialization code in vga-to-spi-tft", "Show data processing logic in vga-to-spi-tft", "Find utility functions in vga-to-spi-tft", "Explain the architecture of vga-to-spi-tft"]}, "z80emu": {"analysis": {"name": "z80emu", "languages": ["C#"], "file_types": {".config": 1, ".csproj": 5, ".cs": 78, ".txt": 6, ".dll": 3, ".cache": 19, ".rom": 3, ".sln": 1, ".png": 2, ".json": 9, ".pdb": 2, ".props": 4, ".targets": 4, ".nuspec": 1, ".editorconfig": 4, ".xaml": 9, ".ttinclude": 3, ".tt": 2, ".lref": 1, ".data": 1, ".hex": 5}, "key_files": [], "functions": ["InvalidOperationException", "Program", "OnCreate", "OnUpdate", "Z80", "Point", "SimpleBus", "SetUpMachineState", "Main", "Sprite", "Write", "Read", "colour", "ReadPeripheral", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "NormalPixel", "GetScreen", "for", "InvertedPixel", "ReadAttributesForCharacter", "WritePeripheral", "GetColouredPixel", "SimpleBus", "GetPixel", "GetPage", "LoadZ80Snapshot", "FileStream", "register", "using", "GetSnapshotType", "LoadZ80File", "for", "Header", "joystick", "mode", "Z80Snapshot", "Write", "WritePeripheral", "Read", "ReadPeripheral", "ArgumentNullException", "Instruction", "IMP", "RELS", "REL", "IMX", "RGIHL", "IDX", "REG", "IMM", "DECIXH", "INCHL", "ANDIXH", "ANDN", "ADCAIXDN", "ADDIXPP", "ANDIYH", "SUBAIYH", "INCIY", "ADDAIYL", "ORR", "DECHL", "SUBAIYL", "DECIXL", "XORR", "CPIYD", "DECIYH", "ADDAIXL", "ANDIXD", "SUBAIXDN", "RESBR", "BITHL", "RESHL", "BITIYD", "RESIXD", "SetFlag", "SETBR", "BITBR", "RESIYD", "SETHL", "SETIXD", "BITIXD", "SETIYD", "Reset", "RunTo", "Interrupt", "SetQ", "UnhaltIfHalted", "ReadFromBusPort", "ReadFromBus", "Instruction", "FourByteOpCode", "Fetch2", "HandleInterrupts", "IncrementRefreshRegister", "ThreeByteOpCode", "GetInstruction", "NonMaskableInterrupt", "Fetch1", "table", "Step", "ConnectToBus", "CultureInfo", "SetComparisonFlags", "SetIncFlags", "IncRegisterPair", "SetShiftRightLogicalFlags", "Add16", "Assign<PERSON><PERSON>R<PERSON><PERSON>", "ReadFromRegister", "SetQ", "WriteToRegisterPair", "Add8", "SetShiftArithmeticFlags", "SetRotateFlags", "GetPageZeroAddress", "And", "Add16WithCarry", "ReadFromRegisterPair", "SetFlag", "Sub16", "Parity", "CheckFlag", "IM0", "SetComparisonFlags", "CPI", "CPDR", "IM2", "LDDR", "SetQ", "SCF", "IM1", "EXSPHL", "CPIR", "CCF", "LDD", "NEG", "sequence", "LDIR", "NOP", "<PERSON><PERSON>", "HALT", "LDI", "INR", "INA", "IND", "OUTI", "INI", "INDR", "SetFlag", "OTDR", "OUTA", "OUTR", "INIR", "OUTD", "SetQ", "OTIR", "RET", "JPIY", "JRNC", "JRC", "JPHL", "RST", "JPCCNN", "CALLCC", "RETCC", "JPNN", "JRZ", "RETI", "JRNZ", "RETN", "CALL", "DJNZ", "JPIX", "PUSHIX", "LDDEA", "LDDEFNN", "LDSPIY", "LDAI", "LDRR", "LDIXNN", "LDSPNN", "LDIXLR", "LDRIXL", "LDRA", "LDIXFNN", "LDHLR", "LDRIYH", "LDIYHIYL", "LDHLN", "LDDENN", "POPHL", "LDIYHR", "PUSHAF", "RLIYD", "RRIXD", "RLD", "SRLHL", "RRD", "Assign<PERSON><PERSON>R<PERSON><PERSON>", "SetQ", "SetRotateRightFlags", "RLCHL", "RLCIXD", "SRAIYD", "RRCHL", "RLCA", "RLCIYD", "SLSHL", "SRLR", "SLSIYD", "RLA", "RRCR", "RRR", "Write", "Read", "ReadPeripheral", "BasicBus", "WritePeripheral", "ControlPanel", "CpuFlags", "CpuStatus", "Disassembler", "Coerce_Screen", "Changed_Screen", "Changed_RawMemory", "MsxScreen", "Coerce_RawMemory", "CoerceAllProperties", "file", "FrameworkPropertyMetadata", "Constructed__MsxScreen", "Changed_MemoryMapRow", "Raise_Mode", "Raise_IndexY", "PropertyChangedEventArgs", "OnPropertyChanged", "Raise_XBit", "Raise_MemoryMapRow", "Changed_SignBit", "Raise_BCPairPrime", "Changed_BCPair", "Changed_XBit", "Raise_Memory", "Raise_ProgramCounter", "Raise_Refresh", "Raise_InterruptVector", "Changed_ProgramCounter", "Raise_SelectedRow", "Raise_HalfCarryBit", "Raise_CarryBit", "Raise_NegationBit", "static", "MainWindowViewModel", "MainWindow", "SetRegisterPairs", "Changed_HLPair", "Changed_NegationBit", "Changed_ProgramCounter", "Changed_IndexY", "Changed_AccuFlags", "Changed_HLPairPrime", "Execute_LoadRomCommand", "file", "BasicBus", "Changed_BCPairPrime", "CanExecute_LoadRomCommand", "Changed_Refresh", "SetFlags", "GetMemoryMapRow", "Changed_ZeroBit", "GetInterruptMode", "Changed_DEPair", "Changed_DEPairPrime", "CanExecute_StepCommand", "MemoryMap", "Int32Rect", "for", "WriteableBitmap", "OnRefresh", "Constructed__MsxScreen", "DispatcherTimer", "FirePropertyChangedNotifications", "GetEnumerator", "MoveNext", "ArgumentOutOfRangeException", "GetObjectData", "ArgumentNullException", "DoClearEntries", "SetEntry", "Enumerator", "DoSetEntry", "InvalidOperationException", "ArgumentException", "GetKeyForItem", "AddEntry", "ValidateCurrent", "RemoveEntry", "Remove", "PropertyChangedEventArgs", "OnCollectionChanged", "Dispose", "StackDisplay", "Execute", "EventArgs", "_canExecute", "RefreshCanExecute", "UserCommand", "CanExecute", "Main", "InitializeComponent", "InitializeComponent", "InitializeComponent", "InitializeComponent", "InitializeComponent", "CreateDelegate", "AddEventHandler", "GetPropertyValue", "SetPropertyValue", "CreateInstance", "_CreateDelegate", "InitializeComponent", "InitializeComponent", "InitializeComponent", "InitializeComponent", "InvalidOperationException", "SimpleBus", "Z80", "Main", "Write", "Read", "ReadPeripheral", "WritePeripheral", "SimpleBus", "EightBitBCDSubtraction", "EightBitBCDAddition", "EightBitBCDSubtractionNegativeResult", "Z80", "answer", "PopProgramCounterForRETCC_GivenCarryFlagSet", "PushAndSetProgramCounterForCALL", "Update<PERSON><PERSON>ory", "PushAndSetProgramCounterForRST", "PushAndSetProgramCounterForCALLCC_GivenZero", "Z80", "DoNothingForRETCC_GivenCarryFlagNotSet", "DoNothingForCALLCC_GivenNotZero", "PopProgramCounterForRET", "UnsetZeroFlagWhenFalseForCPIXD2", "SetZeroFlagWhenTrueForCPHL", "UnsetZeroFlagWhenFalseForCPR1", "SetZeroFlagWhenTrueForCPN", "CompareAccumulatorWithBWithCPR", "UnsetZeroFlagWhenFalseForCPHL1", "Z80", "SetZeroFlagWhenTrueForCPR", "UnsetZeroFlagWhenFalseForCPN2", "SetZeroFlagWhenTrueForCPIXD", "UnsetZeroFlagWhenFalseForCPIYD1", "UnsetZeroFlagWhenFalseForCPNL2", "SetZeroFlagWhenTrueForCPIYD", "UnsetZeroFlagWhenFalseForCPIYD2", "UnsetZeroFlagWhenFalseForCPIXD1", "UnsetZeroFlagWhenFalseForCPR2", "CompareAccumulatorWithItselfWithCPR", "UnsetZeroFlagWhenFalseForCPN1", "DisassembleDDCBandFDCBOpcodesCorrectly", "DisassembleArithmetic1HexFileCorrectly", "DisassembleMultiplicationHexFileCorrectly", "Z80", "LoadAWith10WhenAdding4And6LocationPointedToByIYwithCarrySetForOpcodeADCAIXY_GivenDisPositive", "LoadAWith32WhenAdding2And30ForOpcodeADDAN", "Update<PERSON><PERSON>ory", "IncrementValueAtLocationPointedToByIXPlusDFlagsTest2", "IncrementRegisterFlagsTest2", "LoadAWith9WhenAdding4And4FromBAndCarryFlagSetForOpcodeADCAR", "IncrementRegisterFlagsTest1", "LoadAWith10WhenAdding4And6LocationPointedToByIYForOpcodeADDAIYD_GivenDisPositive", "LoadAWith129WhenAdding127And2ForOpcodeADCAN", "LoadAWith9WhenAdding4And4AndCarryFlagSetForOpcodeADCAN", "bits", "IncrementValueAtLocationPointedToByHLFlagsTest2", "LoadAWith8WhenAdding4And4ForOpcodeADCAN", "LoadAWith11WhenAdding4And6LocationPointedToByHLAndCarryFlagSetForOpcodeADCAHL", "LoadAWith10WhenAdding4And6LocationPointedToByIXForOpcodeADDAIXD_GivenDisPositive", "LoadAWith2AndOverfowWhenAdding129And129ForOpcodeADCAN", "LoadAWithZeroWhenAddingZeroAndZeroForOpcodeADDAN", "LoadAWith129WhenAdding127And2ForOpcodeADDAN", "LoadAWithThreeWhenAddingOneAndTwoForOpcodeADCAN", "FlagTestOnAddition3", "FlagTestOnSubtraction1", "Update<PERSON><PERSON>ory", "LoadAWith10WhenSubtracting4FromLocationPointedToByHLForOpcodeSUBAHL", "FlagTestOnSubtraction2", "LoadAWith128WhenSubtracting2From131AndCarryFlagSetForOpcodeSBCAN", "LoadAWith9WhenSubtracting4AndLocationPointedToByHLAndCarryFlagSetForOpcodeSBCAHL", "LoadAWith129WhenSubtracting2From131ForOpcodeSUBAN", "DecrementRegisterFlagsTest2", "LoadAWith5WhenSubtractingLocationPointedToByIXFrom10ForOpcodeSBCAIXD_GivenDisNegative", "DecrementValueAtLocationPointedToByHLFlagsTest", "LoadAWithZeroWhenSubtractingZeroFromZeroForOpcodeSUBAN", "DecrementValueAtLocationPointedToByIXPlusDFlagsTest", "LoadAWithTwoWhenSubtracting8And5AndCarryBitSetForOpcodeSBCAN", "DecrementValueAtLocationPointedToByIYPlusDFlagsTest", "LoadAWithZeroWhenSubtracting129From129ForOpcodeSUBAN", "LoadAWithZeroWhenSubtractingOneFromTwoAndCarryFlagSetForOpcodeSBCAN", "LoadAWithThreeWhenSubtracting8And5ForOpcodeSUBAN", "DecrementRegisterFlagsTest1", "LoadAWith10WhenSubtracting4FromLocationPointedToByIYForOpcodeSUBAIYD_GivenDisPositive", "LoadAWith7WhenSubtracting4FromBAndCarryFlagSetForOpcodeSBCAR", "LoadAwithValueAtMemoryLocationinIXpludDwhenOperationIsLDRIXD_GivenDisZero", "Update<PERSON><PERSON>ory", "LoadAfromRwhenOperationIsLDAR_GivenRisZero", "LoadIfromA", "LoadAccumulatorWithValueInLocationPointedToByDE", "LoadAccumulatorWithValueInLocationPointedToByNN", "LoadMemoryLocationPointedToByNNwithValueInAccumulator", "LoadBfromAwhenOperationIsLDAB", "LoadAfromRwhenOperationIsLDAR_GivenRisPositive", "LoadMemoryLocationPointedToByHLwithValueInAccumulator", "LoadRfromA", "LoadMemoryLocationPointedToByIYplusDwithValueN_GivenDisNegative", "LoadAfromIwhenOperationIsLDAI_GivenIisNegative", "LoadMemoryLocationPointedToByHLwithValueN", "LoadAfromBwhenOperationIsLDBA", "LoadMemoryLocationPointedToByDEwithValueInAccumulator", "LoadMemoryLocationPointedToByIYplusDwithValueInRegister_GivenDisNegative", "LoadAfromIwhenOperationIsLDAI_GivenIisZero", "LoadAwithValueAtMemoryLocationinIXpludDwhenOperationIsLDRIYD_GivenDisNegative", "NoOpwhenOperationIsLDAA", "AndAccumulatorWithRegisterB", "XorAccumulatorWithByte", "OrAccumulatorWithByteAtLocationPointedToByIXPlusD", "XorAccumulatorWithByteAtLocationPointedToByHL", "OrAccumulatorWithByte", "OrAccumulatorWithRegisterB", "XorAccumulatorWithRegisterB", "XorAccumulatorWithByteAtLocationPointedToByIYPlusD", "AndAccumulatorWithByte", "Z80", "OrAccumulatorWithByteAtLocationPointedToByIYPlusD", "AndAccumulatorWithByteAtLocationPointedToByHL", "XorAccumulatorWithItselfZeroesAccumulator", "AndAccumulatorWithByteAtLocationPointedToByIYPlusD", "OrAccumulatorWithByteAtLocationPointedToByHL", "XorAccumulatorWithByteAtLocationPointedToByIXPlusD", "AndAccumulatorWithByteAtLocationPointedToByIXPlusD", "Update<PERSON><PERSON>ory", "SwapLocationPointedToBySPwithHLforEXSPHL", "FlagsUnchanged", "Z80", "SwapAFandAFPrime", "SwapRegistersWithEXX", "SwapLocationPointedToBySPwithIYforEXSPIY", "SwapDEandHL", "SwapLocationPointedToBySPwithIXforEXSPIX", "InitiallyHaveAllFlagsNotSet", "UnSetZeroFlagOnly", "SetCarryAndSignFlagOnly", "SetFlag", "SetCarryFlagOnly", "SetCarryFlag", "ProduceOnesComplementOfAccumulatorForCPL", "SetInterruptMode1", "SetInterruptMode0", "SetCarryFlagUndocumentedFlags1", "SetCarryFlagUndocumentedFlags2", "Z80", "EnableInterruptBySettingIFF", "SetInterruptMode2", "ComplementCarryFlag", "DisableInterruptByResettingIFF", "SetCarryFlagUndocumentedFlags3", "ProduceTwosComplementOfAccumulatorForNEG", "Z80", "Update<PERSON><PERSON>ory", "ReadSimpleHexFileWithOnlySingleDataRecord", "ReadBtyeFromPortForINA", "FlagsUnchanged", "Z80", "ReturnTrueForDDCB06", "ReturnTrueForFDCB06", "Z80", "ReturnTrueForDD09", "ReturnFalseForED", "ReturnTrueForNOP", "ReturnTrueForDD70", "ReturnFalseForDD00", "ReturnTrueForFDCBO206", "DoNotJumpForwardFourForJRNCPositiveSix_CarrySet", "JumpForwardFourForJRNZPositiveSix_NotZeroSet", "JumpToIX", "JumpToIY", "JumpForwardFourForJRCPositiveSix_CarrySet", "LoadProgramCounterWithAddressForJPNN", "NotJumpWhenEvenParityForJPCCNN", "JumpWhenOddParityForJPCCNN", "JumpBackFourForDJNZ_WhenNotZero", "JumpToHL", "NotJumpWhenCarryFlagNotSetForJPCCNN", "JumpWhenCarryFlagSetForJPCCNN", "JumpForwardFourForJRZPositiveSix_ZeroSet", "JumpForwardFourForJRPositiveSix", "JumpForwardFourForJRNCPositiveSix_CarryNotSet", "DoNotJumpForwardFourForJRCPositiveSix_CarryNotSet", "FlagsUnchanged", "Z80", "NotJumpBackFourForDJNZ_WhenZero", "JumpBackFourForJRNegativeFour", "UpdateLocationContentsCorrectlyForRLCIYD", "UpdateLocationContentsCorrectlyForRLHL", "UpdateRegisterCorrectlyForRRR", "UpdatesLocationCorrectlyWhenSRLIYD", "Update<PERSON><PERSON>ory", "UpdateRegisterCorrectlyForRRCR", "UpdatesLocationPointedToByHLCorrectlyWhenSLAHL", "AccumulatorAndLocationCorrectAfterRRD", "UpdatesRegisterCorrectlyWhenSLAR", "UpdateLocationCorrectlyForRRHL", "UpdatesLocationCorrectlyWhenSLAIYD", "UpdateLocationCorrectlyForSRAHL", "UpdateLocationCorrectlyForRRCHL", "UpdateAccumulatorCorrectlyForRLCA", "UpdatesRegisterCorrectlyWhenSRLR", "UpdateLocationContentsCorrectlyForRLCHL", "UpdateLocationContentsCorrectlyForRLIXD", "UpdateLocationCorrectlyForSRAIXD", "UpdateAccumulatorCorrectlyForRRCA", "UpdateLocationContentsCorrectlyForRLCIXD", "AddHLandBCforADDHLBC3", "SubtractCarryWithHLandBCforSBCHLBC", "AddHLandBCforADDCHLBC4", "IncrementIYforINCIY", "SubtractHLandHLforSBCHLHL", "IncrementDEforINCSS", "AddHLandBCforADDHLBC2", "Z80", "DecrementIXforDECIX", "AddIYandBCforADDIYPP", "AddHLandBCforADDHLBC", "AddCarryWithHLandBCforADCHLBC2", "IncrementIXforINCIX", "DecrementDEforDECSS", "AddCarryWithHLandBCforADCHLBC3", "AddWithCarryHLandBCforADCHLBC", "DecrementIYforDECIY", "AddIXandBCforADDIXPP", "Update<PERSON><PERSON>ory", "LoadIXwithNN", "LoadNNwith16BitOperandFromHL", "LoadDEwithNN", "PushIX", "PushAF", "LoadSPwithIX", "PushThenPop", "LoadNNwith16BitOperandFromHL2", "LoadNNwith16BitOperandFromSP", "LoadNNwith16BitOperandFromIY", "LoadHLwith16BitOperandPointedToByAddressNN", "PushDE", "LoadIYwithNN", "PushIY", "PopDE", "LoadNNwith16BitOperandFromDE", "PushHL", "LoadHLwithNN", "PopHL", "Update<PERSON><PERSON>ory", "PassAllValidationTests", "for", "ExecuteArithmeticTestRoutine1Successfully", "Z80", "FuseTester", "HandleStrayOpCodes2", "ExecuteEightBitMultiplication2RoutineSuccessfully", "ExecuteEightBitMultiplicationRoutineSuccessfully", "Write", "Read", "ReadPeripheral", "BasicBus", "WritePeripheral", "InitialiseMemory", "BasicBus", "InitialiseRegisters", "FuseTest", "foreach", "FuseExpected", "InitialiseStates", "Z80", "RunTests", "Results", "ReadFuseTestsFile", "ReadFuseExpectedFile", "Results"], "classes": ["Program", "SimpleBus", "Z80FileReader", "Z80Snapshot", "Instruction", "Z80", "Z80", "Z80", "Z80", "Z80", "Z80", "Z80", "Z80", "Z80", "Z80", "App", "BasicBus", "ControlPanel", "CpuFlags", "CpuStatus", "Disassembler", "MsxScreen", "MainWindowViewModel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MainWindow", "MainWindowViewModel", "MemoryMap", "MsxScreen", "ObservableDictionary", "KeyedDictionaryEntryCollection", "Enumerator", "StackDisplay", "UserCommand", "App", "ControlPanel", "CpuFlags", "CpuStatus", "Disassembler", "GeneratedInternalTypeHelper", "MainWindow", "MemoryMap", "MsxScreen", "StackDisplay", "Program", "SimpleBus", "BinaryCodedDecimalArithmeticTests", "CallAndReturnGroupShould", "ComparisonsShould", "DisassemblerShould", "EightBitArithmeticLogicADDGroupShould", "EightBitArithmeticLogicSUBGroupShould", "EightBitLoadGroupShould", "EightBitLogicGroupShould", "ExchangeShould", "FlagsShould", "GeneralControlGroupShould", "HexFileReaderTests", "InputOutputShould", "IsSupportedShould", "JumpGroupShould", "RotateAndShiftGroupShould", "SixteenBitArithmeticLogicGroupShould", "SixteenBitLoadGroupShould", "Z80EmulatorShould", "BasicBus", "FuseTest", "FuseExpected", "FuseTester", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Results"], "modules": [], "keywords": ["debug", "trace", "http", "flag", "log", "event", "connection", "memory", "setting", "timer", "option", "handle", "serial", "exception", "error", "sync", "lock", "buffer", "config", "thread", "parameter"], "total_files": 163, "total_lines": 25684}, "questions": ["Find memory management functions in z80emu", "Find error handling patterns in z80emu", "Locate configuration management in z80emu", "Find timer and timeout handling in z80emu", "Show concurrency and threading code in z80emu"]}}