#!/usr/bin/env python3
"""
Debug Metadata Values
Check what's actually stored in the chunk metadata for complexity and quality scores
"""

import requests
import json

def debug_metadata_values():
    """Debug the actual metadata values in chunks"""
    print("🔍 Debugging Metadata Values")
    print("=" * 60)
    
    # Get a few chunks to examine their metadata
    try:
        response = requests.post(
            "http://home-ai-server.local:5002/search",
            json={
                "query": "interface",
                "codebase_name": "bookstore",
                "n_results": 5
            },
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get("results", [])
            
            print(f"📊 Found {len(results)} chunks to examine")
            
            for i, result in enumerate(results):
                metadata = result.get("metadata", {})
                
                print(f"\n🔍 **Chunk {i+1}:**")
                print(f"   • File: {metadata.get('file_path', 'unknown')}")
                print(f"   • Lines: {metadata.get('start_line', '?')}-{metadata.get('end_line', '?')}")
                
                # Check complexity_score
                complexity_score = metadata.get('complexity_score')
                print(f"   • complexity_score: {repr(complexity_score)} (type: {type(complexity_score).__name__})")
                
                # Check quality_score  
                quality_score = metadata.get('quality_score')
                print(f"   • quality_score: {repr(quality_score)} (type: {type(quality_score).__name__})")
                
                # Check chunk_type
                chunk_type = metadata.get('chunk_type')
                print(f"   • chunk_type: {repr(chunk_type)} (type: {type(chunk_type).__name__})")
                
                # Check type field
                type_field = metadata.get('type')
                print(f"   • type: {repr(type_field)} (type: {type(type_field).__name__})")
                
                # Try to convert string values to float
                if isinstance(complexity_score, str):
                    try:
                        numeric_complexity = float(complexity_score)
                        print(f"   • complexity_score as float: {numeric_complexity}")
                        
                        # Categorize
                        if numeric_complexity <= 0.2:
                            category = 'low'
                        elif numeric_complexity <= 0.5:
                            category = 'medium'
                        elif numeric_complexity <= 0.8:
                            category = 'high'
                        else:
                            category = 'very_high'
                        print(f"   • complexity category: {category}")
                        
                    except (ValueError, TypeError) as e:
                        print(f"   • complexity conversion error: {e}")
                
                if isinstance(quality_score, str):
                    try:
                        numeric_quality = float(quality_score)
                        print(f"   • quality_score as float: {numeric_quality}")
                        
                        # Categorize
                        if numeric_quality <= 0.2:
                            category = 'poor'
                        elif numeric_quality <= 0.4:
                            category = 'fair'
                        elif numeric_quality <= 0.6:
                            category = 'good'
                        elif numeric_quality <= 0.8:
                            category = 'very_good'
                        else:
                            category = 'excellent'
                        print(f"   • quality category: {category}")
                        
                    except (ValueError, TypeError) as e:
                        print(f"   • quality conversion error: {e}")
                
                print(f"   • Raw metadata keys: {list(metadata.keys())}")
                
        else:
            print(f"❌ Search failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_manual_categorization():
    """Test manual categorization of known values"""
    print(f"\n🧪 Testing Manual Categorization")
    print("=" * 60)
    
    test_values = [
        ("0.5", "string"),
        (0.5, "float"),
        ("0.2", "string"),
        (0.8, "float"),
        ("unknown", "string"),
        (None, "NoneType")
    ]
    
    for value, value_type in test_values:
        print(f"\n🔍 Testing value: {repr(value)} ({value_type})")
        
        # Test complexity categorization
        if isinstance(value, (int, float)):
            if value <= 0.2:
                complexity_category = 'low'
            elif value <= 0.5:
                complexity_category = 'medium'
            elif value <= 0.8:
                complexity_category = 'high'
            else:
                complexity_category = 'very_high'
        elif isinstance(value, str):
            try:
                numeric_score = float(value)
                if numeric_score <= 0.2:
                    complexity_category = 'low'
                elif numeric_score <= 0.5:
                    complexity_category = 'medium'
                elif numeric_score <= 0.8:
                    complexity_category = 'high'
                else:
                    complexity_category = 'very_high'
            except (ValueError, TypeError):
                complexity_category = str(value)
        else:
            complexity_category = str(value)
        
        print(f"   • Complexity category: {complexity_category}")

def main():
    """Main debug function"""
    print("🚨 Debugging Metadata Values Issue")
    print("=" * 80)
    
    try:
        debug_metadata_values()
        test_manual_categorization()
        
        print(f"\n🎯 **Debug Summary:**")
        print(f"   • Examined actual metadata from ChromaDB")
        print(f"   • Tested type conversion logic")
        print(f"   • Identified root cause of 'unknown' values")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")

if __name__ == "__main__":
    main()
