#!/usr/bin/env python3
"""
Test Enhanced Bulk Rebuild Integration
Test both the main API and web interface integration
"""

import requests
import json
import time

def test_main_api_bulk_rebuild():
    """Test the main API bulk rebuild endpoint"""
    print("🧪 Testing Main API Enhanced Bulk Rebuild")
    print("=" * 60)
    
    try:
        start_time = time.time()
        
        # Test the main API endpoint
        response = requests.post(
            "http://home-ai-server.local:5002/tools/bulk_rebuild_all_codebases",
            json=["build", "test", "bin", "obj", "__pycache__", ".git", ".svn", "node_modules", "dist"],
            timeout=600
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ **Main API Test SUCCESSFUL**")
            print(f"   • Status: {response.status_code}")
            print(f"   • Success: {data.get('success', False)}")
            print(f"   • Total codebases: {data.get('total_codebases', 0)}")
            print(f"   • Successful: {data.get('successful_count', 0)}")
            print(f"   • Failed: {data.get('failed_count', 0)}")
            print(f"   • Total time: {data.get('total_time', 0):.1f}s")
            print(f"   • API response time: {end_time - start_time:.1f}s")
            
            # Show sample results
            results = data.get('results', [])
            if results:
                print(f"\n📊 **Sample Results:**")
                for i, result in enumerate(results[:3]):  # Show first 3
                    codebase = result.get('codebase', 'unknown')
                    success = result.get('success', False)
                    if success:
                        stats = result.get('stats', {})
                        chunks = stats.get('chunks', 0)
                        languages = stats.get('languages', [])
                        tree_sitter = stats.get('tree_sitter_info', 'unknown')
                        print(f"   {i+1}. ✅ {codebase}: {chunks} chunks, {len(languages)} languages")
                        print(f"      🌳 {tree_sitter}")
                    else:
                        error = result.get('error', 'Unknown error')
                        print(f"   {i+1}. ❌ {codebase}: {error}")
            
            return True
            
        else:
            print(f"❌ **Main API Test FAILED**")
            print(f"   • Status: {response.status_code}")
            print(f"   • Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ **Main API Test ERROR**: {e}")
        return False

def test_web_interface_integration():
    """Test the web interface integration (if available)"""
    print(f"\n🌐 Testing Web Interface Integration")
    print("=" * 60)
    
    try:
        # Test if web interface is available
        health_response = requests.get("http://home-ai-server.local:5003/health", timeout=5)
        
        if health_response.status_code != 200:
            print(f"⚠️ **Web Interface Not Available**")
            print(f"   • Status: {health_response.status_code}")
            print(f"   • Skipping web interface test")
            return False
        
        print(f"✅ **Web Interface Available**")
        
        # Test the enhanced bulk rebuild endpoint
        start_time = time.time()
        
        response = requests.post(
            "http://home-ai-server.local:5003/api/vector_db/enhanced_bulk_rebuild",
            timeout=600
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ **Web Interface Test SUCCESSFUL**")
            print(f"   • Status: {response.status_code}")
            print(f"   • Success: {data.get('success', False)}")
            print(f"   • Operation: {data.get('operation', 'unknown')}")
            print(f"   • Total codebases: {data.get('total_codebases', 0)}")
            print(f"   • Successful: {data.get('successful_count', 0)}")
            print(f"   • Failed: {data.get('failed_count', 0)}")
            print(f"   • Total time: {data.get('total_time', 0):.1f}s")
            print(f"   • Web response time: {end_time - start_time:.1f}s")
            
            # Show enhanced features
            enhanced_features = data.get('enhanced_features', {})
            if enhanced_features:
                print(f"\n🚀 **Enhanced Features:**")
                for feature, enabled in enhanced_features.items():
                    status = "✅" if enabled else "❌"
                    print(f"   • {status} {feature.replace('_', ' ').title()}")
            
            return True
            
        else:
            print(f"❌ **Web Interface Test FAILED**")
            print(f"   • Status: {response.status_code}")
            print(f"   • Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"⚠️ **Web Interface Not Running**")
        print(f"   • Connection refused to port 5003")
        print(f"   • Skipping web interface test")
        return False
    except Exception as e:
        print(f"❌ **Web Interface Test ERROR**: {e}")
        return False

def test_feature_comparison():
    """Compare features between standalone script and integrated API"""
    print(f"\n🔍 Feature Comparison")
    print("=" * 60)
    
    features = {
        "Enhanced Statistics": "✅ Both",
        "Tree-sitter Integration": "✅ Both", 
        "Function Detection": "✅ Both",
        "Language Distribution": "✅ Both",
        "Quality Analysis": "✅ Both",
        "Complexity Analysis": "✅ Both",
        "Progress Tracking": "✅ Web Interface",
        "Comprehensive Summary": "✅ Both",
        "Error Handling": "✅ Both",
        "Timeout Management": "✅ Both"
    }
    
    print("📊 **Feature Availability:**")
    for feature, availability in features.items():
        print(f"   • {feature}: {availability}")

def main():
    """Main test function"""
    print("🚀 Enhanced Bulk Rebuild Integration Test")
    print("=" * 80)
    
    # Test main API
    api_success = test_main_api_bulk_rebuild()
    
    # Test web interface
    web_success = test_web_interface_integration()
    
    # Show feature comparison
    test_feature_comparison()
    
    # Final summary
    print(f"\n🎯 **Integration Test Summary:**")
    print("=" * 60)
    print(f"   • Main API Integration: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"   • Web Interface Integration: {'✅ PASSED' if web_success else '⚠️ SKIPPED/FAILED'}")
    
    if api_success:
        print(f"\n🎉 **SUCCESS!** Enhanced bulk rebuild is fully integrated into the system!")
        print(f"   • API endpoint: /tools/bulk_rebuild_all_codebases")
        print(f"   • Web endpoint: /api/vector_db/enhanced_bulk_rebuild")
        print(f"   • Features: Tree-sitter, Statistics, Quality Analysis")
    else:
        print(f"\n❌ **FAILED!** Integration needs attention.")

if __name__ == "__main__":
    main()
