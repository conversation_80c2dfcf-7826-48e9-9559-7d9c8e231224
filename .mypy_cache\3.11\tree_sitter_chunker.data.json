{".class": "MypyFile", "_fullname": "tree_sitter_chunker", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "tree_sitter.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "TreeSitterChunker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tree_sitter_chunker.TreeSitterChunker", "name": "TreeSitterChunker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tree_sitter_chunker", "mro": ["tree_sitter_chunker.TreeSitterChunker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "embedding_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "embedding_model"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TreeSitterChunker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_fallback_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._create_fallback_chunk", "name": "_create_fallback_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_fallback_chunk of TreeSitterChunker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_construct_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._extract_construct_name", "name": "_extract_construct_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "content", "language"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_construct_name of TreeSitterChunker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_function_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "func_node", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._extract_function_name", "name": "_extract_function_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "func_node", "content"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_function_name of TreeSitterChunker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_meaningful_constructs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constructs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._filter_meaningful_constructs", "name": "_filter_meaningful_constructs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "constructs"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_meaningful_constructs of TreeSitterChunker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "content", "functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._find_functions", "name": "_find_functions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "content", "functions"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_functions of TreeSitterChunker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_language_constructs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "node", "content", "language", "constructs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._find_language_constructs", "name": "_find_language_constructs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "node", "content", "language", "constructs"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_language_constructs of TreeSitterChunker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_name_by_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "content", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._find_name_by_pattern", "name": "_find_name_by_pattern", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "content", "pattern"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_name_by_pattern of TreeSitterChunker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_map_node_type_to_chunk_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker._map_node_type_to_chunk_type", "name": "_map_node_type_to_chunk_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_type"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_map_node_type_to_chunk_type of TreeSitterChunker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_c_cpp_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker.chunk_c_cpp_file", "name": "chunk_c_cpp_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chunk_c_cpp_file of TreeSitterChunker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file_path", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker.chunk_file", "name": "chunk_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file_path", "content", "language"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chunk_file of TreeSitterChunker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embedding_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tree_sitter_chunker.TreeSitterChunker.embedding_config", "name": "embedding_config", "type": "embedding_config.EmbeddingModelConfig"}}, "embedding_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tree_sitter_chunker.TreeSitterChunker.embedding_model", "name": "embedding_model", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "language_name", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tree_sitter_chunker.TreeSitterChunker.get_language", "name": "get_language", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "language_name", "file_path"], "arg_types": ["tree_sitter_chunker.TreeSitterChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_language of TreeSitterChunker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "language_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tree_sitter_chunker.TreeSitterChunker.language_cache", "name": "language_cache", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "language_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tree_sitter_chunker.TreeSitterChunker.language_map", "name": "language_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tree_sitter_chunker.TreeSitterChunker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tree_sitter_chunker.TreeSitterChunker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tree_sitter_chunker.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tree_sitter_chunker.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tree_sitter_chunker.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tree_sitter_chunker.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tree_sitter_chunker.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tree_sitter_chunker.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "estimate_tokens": {".class": "SymbolTableNode", "cross_ref": "embedding_config.estimate_tokens", "kind": "Gdef"}, "get_embedding_config": {".class": "SymbolTableNode", "cross_ref": "embedding_config.get_embedding_config", "kind": "Gdef"}, "get_language": {".class": "SymbolTableNode", "cross_ref": "tree_sitter_language_pack.get_language", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\tree_sitter_chunker.py"}