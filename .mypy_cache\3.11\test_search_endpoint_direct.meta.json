{"data_mtime": 1752681811, "dep_lines": [6, 7, 91, 93, 98, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "json", "sys", "open_webui_code_analyzer_tool", "asyncio", "builtins", "_frozen_importlib", "_typeshed", "abc", "http", "http.cookiejar", "requests.auth", "requests.models", "types", "typing"], "hash": "cb0ac7f282c2d302d922e20bc93434fde61bd49f", "id": "test_search_endpoint_direct", "ignore_all": false, "interface_hash": "bf2bf220d8c37b1bdeb360cf86f3fef597d79bf2", "mtime": 1752681808, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_search_endpoint_direct.py", "plugin_data": null, "size": 5854, "suppressed": [], "version_id": "1.15.0"}