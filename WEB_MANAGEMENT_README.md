# Code Analyzer Web Management Server

A comprehensive web-based management interface for the Code Analyzer Server running on port 5002.

## 🎯 **Overview**

The Web Management Server provides a modern, responsive dashboard to monitor and control your Code Analyzer Server. It runs on **port 5003** and offers real-time monitoring, codebase management, and server controls.

## ✨ **Features**

### 📊 **Real-time Monitoring**
- **Server Health**: Live health status, version info, response times
- **System Validation**: Component status and system validation
- **Performance Metrics**: Endpoint response times and success rates
- **Auto-refresh**: 30-second automatic data refresh

### 📚 **Codebase Management**
- **Codebase Status**: View all available codebases and their status
- **Codebase Selection**: Switch active codebase for queries
- **Statistics**: Detailed codebase statistics and metrics

### ⚡ **GPU Infrastructure**
- **GPU Status**: Real-time GPU availability and specifications
- **Hardware Details**: GPU types, tiers, and capabilities
- **Resource Monitoring**: GPU utilization and availability

### 🧪 **Testing & Control**
- **Query Testing**: Test queries against the analyzer server
- **Server Controls**: Codebase selection and management
- **Response Monitoring**: View query results and performance

## 🚀 **Quick Start**

### **1. Setup**
```bash
# Run the setup script
python3 setup_web_management.py

# This will:
# - Create necessary directories
# - Install required dependencies
# - Create configuration files
# - Generate startup scripts
```

### **2. Start the Server**
```bash
# Option 1: Direct Python
python3 web_management_server.py

# Option 2: Using startup script
./start_web_management.sh

# Option 3: Docker Compose (see Docker section)
docker-compose -f docker-compose-web-management.yml up
```

### **3. Access Dashboard**
Open your browser and navigate to:
- **Local**: http://localhost:5003
- **Remote**: http://home-ai-server.local:5003

## 📋 **Requirements**

### **Python Dependencies**
- `fastapi` - Web framework
- `uvicorn[standard]` - ASGI server
- `jinja2` - Template engine
- `python-multipart` - Form handling
- `requests` - HTTP client

### **System Requirements**
- Python 3.8+
- Code Analyzer Server running on port 5002
- Network access to the analyzer server

## 🔧 **Configuration**

### **Configuration File: `web_management_config.json`**
```json
{
  "code_analyzer_server": {
    "host": "localhost",
    "port": 5002,
    "base_url": "http://localhost:5002"
  },
  "web_management_server": {
    "host": "0.0.0.0",
    "port": 5003,
    "title": "Code Analyzer Management Interface",
    "auto_refresh_interval": 30
  },
  "features": {
    "server_restart": false,
    "log_monitoring": true,
    "performance_metrics": true,
    "codebase_management": true,
    "query_testing": true
  }
}
```

### **Environment Variables**
- `CODE_ANALYZER_BASE_URL` - Base URL of the analyzer server
- `WEB_MANAGEMENT_PORT` - Port for the web interface (default: 5003)

## 🐳 **Docker Deployment**

### **Add to existing docker-compose.yml:**
```yaml
services:
  web-management:
    build: .
    ports:
      - "5003:5003"
    volumes:
      - .:/app
    working_dir: /app
    command: python3 web_management_server.py
    depends_on:
      - code-analyzer-server
    environment:
      - CODE_ANALYZER_BASE_URL=http://code-analyzer-server:5002
    networks:
      - analyzer-network
```

### **Standalone Docker:**
```bash
# Build and run
docker build -t code-analyzer-web-management .
docker run -p 5003:5003 -e CODE_ANALYZER_BASE_URL=http://host.docker.internal:5002 code-analyzer-web-management
```

## 🔧 **API Endpoints**

### **Health & Status**
- `GET /` - Main dashboard
- `GET /api/health` - Server health status
- `GET /api/metrics` - Performance metrics

### **Codebase Management**
- `GET /api/codebases` - List all codebases
- `POST /api/control/select_codebase` - Select active codebase

### **GPU Infrastructure**
- `GET /api/gpu` - GPU status and specifications

### **Testing & Control**
- `POST /api/test/query` - Test query execution
- `POST /api/control/restart` - Server restart (if enabled)

## 📊 **Dashboard Components**

### **1. Server Health Card**
- Online/offline status indicator
- Server version and response time
- System validation status
- Component health checks

### **2. Codebases Card**
- Total codebase count
- Individual codebase status
- Processing statistics
- Last update timestamps

### **3. GPU Infrastructure Card**
- GPU availability status
- Number of available GPUs
- GPU specifications and tiers
- Hardware details

### **4. Performance Metrics Card**
- Average response times
- Endpoint success rates
- Performance trends
- System load indicators

### **5. Control Panel**
- Codebase selection dropdown
- Query testing interface
- Server control buttons
- Real-time output display

## 🔒 **Security Considerations**

### **Network Security**
- Runs on internal network by default
- No external authentication (add if needed)
- CORS protection for API endpoints

### **Access Control**
- Consider adding authentication for production
- Restrict network access as needed
- Monitor access logs

## 🛠️ **Customization**

### **Styling**
- Modify `templates/dashboard.html` for UI changes
- Add custom CSS in the `<style>` section
- Responsive design works on mobile devices

### **Features**
- Add new API endpoints in `web_management_server.py`
- Extend dashboard with additional cards
- Implement custom monitoring metrics

### **Integration**
- Connect to logging systems
- Add alerting capabilities
- Integrate with monitoring tools

## 📈 **Monitoring & Maintenance**

### **Health Checks**
- Automatic server health monitoring
- Real-time status updates
- Error detection and reporting

### **Performance Monitoring**
- Response time tracking
- Endpoint success rate monitoring
- Resource utilization metrics

### **Maintenance Tasks**
- Regular dependency updates
- Log rotation and cleanup
- Performance optimization

## 🔧 **Troubleshooting**

### **Common Issues**

**1. Connection Refused**
```
Error: Connection refused - Server may be down
```
- Ensure Code Analyzer Server is running on port 5002
- Check network connectivity
- Verify firewall settings

**2. Template Not Found**
```
TemplateNotFound: dashboard.html
```
- Run `python3 setup_web_management.py` to create templates
- Ensure templates directory exists
- Check file permissions

**3. Dependency Issues**
```
ModuleNotFoundError: No module named 'fastapi'
```
- Run setup script to install dependencies
- Use virtual environment if needed
- Check Python version compatibility

### **Debug Mode**
```bash
# Run with debug logging
uvicorn web_management_server:app --host 0.0.0.0 --port 5003 --log-level debug
```

## 🎯 **Future Enhancements**

### **Planned Features**
- [ ] Real-time log monitoring
- [ ] Alert system for server issues
- [ ] Historical performance graphs
- [ ] User authentication system
- [ ] API rate limiting
- [ ] Export functionality for metrics
- [ ] Mobile app companion

### **Integration Opportunities**
- [ ] Prometheus metrics export
- [ ] Grafana dashboard integration
- [ ] Slack/Discord notifications
- [ ] Email alerting system
- [ ] CI/CD pipeline integration

## 📞 **Support**

For issues, questions, or feature requests:
1. Check the troubleshooting section
2. Review server logs for errors
3. Verify configuration settings
4. Test network connectivity

## 📄 **License**

This web management interface is part of the Code Analyzer Server project and follows the same licensing terms.
