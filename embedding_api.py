"""
Embedding Model Configuration API
Provides endpoints to configure and manage embedding models
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
from embedding_config import embedding_config_manager, EmbeddingModelConfig
import os

router = APIRouter(prefix="/embedding", tags=["embedding"])

class EmbeddingModelRequest(BaseModel):
    model_name: str

class EmbeddingModelResponse(BaseModel):
    success: bool
    message: str
    config: Optional[Dict[str, Any]] = None

@router.get("/models")
async def list_embedding_models():
    """List all available embedding models"""
    try:
        models = embedding_config_manager.list_available_models()
        current_model = embedding_config_manager.get_current_model()
        
        return {
            "success": True,
            "current_model": current_model,
            "available_models": {
                name: {
                    "name": config.name,
                    "max_tokens": config.max_tokens,
                    "dimensions": config.dimensions,
                    "description": config.description,
                    "context_window": config.context_window,
                    "recommended_chunk_size": config.recommended_chunk_size,
                    "safe_chunk_size": config.safe_chunk_size
                }
                for name, config in models.items()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list models: {str(e)}")

@router.get("/current")
async def get_current_embedding_model():
    """Get current embedding model configuration"""
    try:
        config = embedding_config_manager.get_current_config()
        current_model = embedding_config_manager.get_current_model()
        
        return {
            "success": True,
            "model_name": current_model,
            "config": {
                "name": config.name,
                "max_tokens": config.max_tokens,
                "dimensions": config.dimensions,
                "description": config.description,
                "context_window": config.context_window,
                "recommended_chunk_size": config.recommended_chunk_size,
                "safe_chunk_size": config.safe_chunk_size
            },
            "summary": embedding_config_manager.get_model_summary()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get current model: {str(e)}")

@router.post("/set")
async def set_embedding_model(request: EmbeddingModelRequest) -> EmbeddingModelResponse:
    """Set the embedding model"""
    try:
        success = embedding_config_manager.set_model(request.model_name)
        
        if success:
            config = embedding_config_manager.get_config(request.model_name)
            return EmbeddingModelResponse(
                success=True,
                message=f"Successfully set embedding model to {request.model_name}",
                config={
                    "name": config.name,
                    "max_tokens": config.max_tokens,
                    "dimensions": config.dimensions,
                    "description": config.description,
                    "context_window": config.context_window,
                    "recommended_chunk_size": config.recommended_chunk_size,
                    "safe_chunk_size": config.safe_chunk_size
                }
            )
        else:
            return EmbeddingModelResponse(
                success=False,
                message=f"Unknown embedding model: {request.model_name}"
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to set model: {str(e)}")

@router.get("/validate_chunk")
async def validate_chunk_size(text: str, model_name: Optional[str] = None):
    """Validate if chunk size is appropriate for embedding model"""
    try:
        config = embedding_config_manager.get_config(model_name or embedding_config_manager.get_current_model())
        token_count = embedding_config_manager.estimate_tokens(text)
        is_valid = embedding_config_manager.is_chunk_size_valid(text, model_name)
        
        return {
            "success": True,
            "model_name": config.name,
            "text_length": len(text),
            "estimated_tokens": token_count,
            "max_tokens": config.max_tokens,
            "safe_chunk_size": config.safe_chunk_size,
            "is_valid": is_valid,
            "recommendation": "✅ Chunk size is valid" if is_valid else f"❌ Chunk too large. Max safe size: {config.safe_chunk_size} tokens"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to validate chunk: {str(e)}")

@router.get("/stats")
async def get_embedding_stats():
    """Get embedding model statistics and recommendations"""
    try:
        config = embedding_config_manager.get_current_config()
        current_model = embedding_config_manager.get_current_model()
        
        # Calculate some useful statistics
        char_to_token_ratio = 4  # Rough estimate
        max_chars = config.max_tokens * char_to_token_ratio
        safe_chars = config.safe_chunk_size * char_to_token_ratio
        
        return {
            "success": True,
            "model_name": current_model,
            "token_limits": {
                "max_tokens": config.max_tokens,
                "safe_chunk_size": config.safe_chunk_size,
                "recommended_chunk_size": config.recommended_chunk_size
            },
            "character_estimates": {
                "max_characters": max_chars,
                "safe_characters": safe_chars,
                "char_to_token_ratio": char_to_token_ratio
            },
            "recommendations": {
                "chunk_strategy": "Use function-level chunking with Tree-sitter",
                "max_chunk_size": f"{config.safe_chunk_size} tokens ({safe_chars} characters)",
                "optimal_range": f"50-{config.safe_chunk_size} tokens for best performance"
            },
            "model_info": {
                "dimensions": config.dimensions,
                "context_window": config.context_window,
                "description": config.description
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

# Add router to main app
def setup_embedding_routes(app):
    """Setup embedding routes in the main FastAPI app"""
    app.include_router(router)
