FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies (including web management dependencies)
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir fastapi jinja2 python-multipart

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p templates && \
    mkdir -p /var/log/supervisor && \
    mkdir -p /var/run && \
    chmod 755 /var/log/supervisor

# Create supervisor configuration
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose both ports
EXPOSE 5002 5003

# Health check for both services
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:5002/health && curl -f http://localhost:5003/api/health || exit 1

# Set Python to unbuffered mode for immediate output
ENV PYTHONUNBUFFERED=1

# Command to run both applications via supervisor
<PERSON><PERSON> ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]