{"data_mtime": 1752803043, "dep_lines": [9, 10, 8, 11, 12, 13, 14, 15, 16, 17, 215, 284, 940, 975, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 5, 5, 10, 20, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.responses", "fastapi.templating", "<PERSON><PERSON><PERSON>", "requests", "json", "time", "os", "datetime", "typing", "u<PERSON><PERSON>", "re", "psutil", "math", "uuid", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "asyncio", "asyncio.protocols", "configparser", "contextlib", "enum", "fastapi.applications", "fastapi.exceptions", "fastapi.params", "fastapi.routing", "http", "http.cookiejar", "io", "jinja2", "jinja2.environment", "json.decoder", "json.encoder", "psutil._common", "psutil._pswindows", "requests.auth", "requests.exceptions", "requests.models", "starlette", "starlette.applications", "starlette.background", "starlette.exceptions", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.templating", "starlette.websockets", "types", "typing_extensions", "uvicorn._types"], "hash": "ecf375045a53f00b3d577f6cecb15773d4468328", "id": "web_management_server", "ignore_all": false, "interface_hash": "51864c8e8ef52abaf4d3ef50c7464bc29cd605dd", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\web_management_server.py", "plugin_data": null, "size": 74405, "suppressed": [], "version_id": "1.15.0"}