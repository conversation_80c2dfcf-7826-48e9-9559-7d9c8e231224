#!/usr/bin/env python3
"""
Bulk Rebuild All Vector Databases with Tree-sitter Function-Level Chunking
This script rebuilds all codebases to apply the new Tree-sitter integration
"""

import requests
import json
import time
import re
from typing import List, Dict, Union, Any

# Configuration
CODE_ANALYZER_URL = "http://home-ai-server.local:5002"
TIMEOUT_PER_CODEBASE = 300  # 5 minutes per codebase
EXCLUDE_DIRS = ["build", "test", "bin", "obj", "__pycache__", ".git", ".svn", "node_modules", "dist"]

def get_all_codebases() -> List[str]:
    """Get list of all available codebases"""
    try:
        response = requests.get(f"{CODE_ANALYZER_URL}/tools/list_codebases", timeout=30)
        if response.status_code == 200:
            # Parse the result to extract codebase names
            # The response contains formatted text, so we need to extract names
            result_text = response.json().get("result", "")

            # Extract codebase names from the formatted response
            codebases = []
            lines = result_text.split('\n')
            for line in lines:
                if line.startswith('**✅ ') or line.startswith('**🚀 '):
                    # Extract name between ** markers
                    name = line.split('**')[1].replace('✅ ', '').replace('🚀 ', '').strip()
                    if name:
                        codebases.append(name)

            return codebases
        else:
            print(f"❌ Failed to get codebases list: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error getting codebases: {e}")
        return []

def get_current_chunk_counts() -> Dict[str, int]:
    """Get current chunk counts for all codebases before rebuild"""
    try:
        response = requests.get(f"{CODE_ANALYZER_URL}/tools/list_codebases", timeout=30)
        if response.status_code == 200:
            result_text = response.json().get("result", "")
            chunk_counts = {}

            # Parse chunk counts from the response
            lines = result_text.split('\n')
            current_codebase = None

            for line in lines:
                if line.startswith('**✅ '):
                    # Extract codebase name
                    current_codebase = line.split('**')[1].replace('✅ ', '').strip()
                elif current_codebase and 'Chunks:' in line:
                    # Extract chunk count
                    chunk_match = re.search(r'Chunks:\s*(\d+)', line)
                    if chunk_match:
                        chunk_counts[current_codebase] = int(chunk_match.group(1))

            return chunk_counts
        else:
            print(f"❌ Failed to get current chunk counts: {response.status_code}")
            return {}
    except Exception as e:
        print(f"❌ Error getting current chunk counts: {e}")
        return {}

def rebuild_codebase(codebase_name: str) -> Dict:
    """Rebuild a single codebase with detailed statistics"""
    print(f"\n🔄 Rebuilding {codebase_name}...")

    try:
        response = requests.post(
            f"{CODE_ANALYZER_URL}/tools/rebuild_codebase",
            json={
                "codebase_name": codebase_name,
                "exclude_dirs": EXCLUDE_DIRS
            },
            timeout=TIMEOUT_PER_CODEBASE
        )

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ {codebase_name}: Successfully rebuilt")

                # Parse the response for statistics
                result_text = data.get('result', '')
                processing_stats = data.get('processing_stats', {})
                deletion_stats = data.get('deletion_stats', {})
                stats = parse_rebuild_statistics(result_text, processing_stats, deletion_stats, codebase_name)

                # Display detailed statistics
                print(f"   📊 Chunks: {stats['chunks']}")
                print(f"   🌍 Languages: {', '.join(stats['languages'])}")
                print(f"   🌳 Tree-sitter: {stats['tree_sitter_info']}")
                print(f"   🔧 Embedding: {stats['embedding_info']}")

                # Show deletion info if available
                if stats.get('chunks_deleted', 0) > 0:
                    print(f"   🗑️ Deleted: {stats['chunks_deleted']} old chunks")

                return {
                    "success": True,
                    "codebase": codebase_name,
                    "stats": stats
                }
            else:
                error = data.get('error', 'Unknown error')
                print(f"❌ {codebase_name}: Rebuild failed - {error}")
                return {"success": False, "error": error, "codebase": codebase_name}
        else:
            print(f"❌ {codebase_name}: HTTP {response.status_code}")
            return {"success": False, "error": f"HTTP {response.status_code}", "codebase": codebase_name}

    except requests.exceptions.Timeout:
        print(f"⏰ {codebase_name}: Timeout after {TIMEOUT_PER_CODEBASE}s")
        return {"success": False, "error": "Timeout", "codebase": codebase_name}
    except Exception as e:
        print(f"❌ {codebase_name}: Error - {e}")
        return {"success": False, "error": str(e), "codebase": codebase_name}

def parse_rebuild_statistics(result_text: str, processing_stats: Dict[str, Any], deletion_stats: Dict[str, Any], codebase_name: str) -> Dict[str, Any]:
    """Parse rebuild response to extract detailed statistics"""
    stats: Dict[str, Any] = {
        "chunks": "unknown",
        "languages": [],
        "tree_sitter_info": "unknown",
        "embedding_info": "unknown",
        "semantic_categories": {},
        "processing_time": "unknown"
    }

    try:
        # Extract chunk count from processing_stats first
        if processing_stats:
            # Get semantic tags for analysis (but don't use for counting)
            semantic_tags = processing_stats.get('semantic_tag_frequency', {})
            if semantic_tags:
                stats["semantic_categories"] = semantic_tags

            # Try to get chunk count from type_distribution (more reliable)
            type_dist = processing_stats.get('type_distribution', {})
            if type_dist:
                # Sum type distribution to get actual chunk count
                total_chunks = sum(type_dist.values())
                if total_chunks > 0:
                    stats["chunks"] = total_chunks

            # Extract language distribution
            lang_dist = processing_stats.get('language_distribution', {})
            if lang_dist:
                # Convert keys to proper case and filter out 'multi'
                languages = []
                for lang, count in lang_dist.items():
                    if lang.lower() != 'multi' and count > 0:
                        languages.append(lang.title())
                stats["languages"] = languages
                stats["language_breakdown"] = lang_dist

            # Extract processing time
            if 'processing_time' in processing_stats:
                stats["processing_time"] = processing_stats['processing_time']

        # Fallback: Extract chunk count from result text
        if stats["chunks"] == "unknown":
            # Try multiple patterns for chunk count
            patterns = [
                r'Chunks created:\s*(\d+)',
                r'Chunks Processed:\s*(\d+)',
                r'Chunks Indexed:\s*(\d+)',
                r'(\d+)\s+chunks'
            ]

            for pattern in patterns:
                chunk_matches = re.findall(pattern, result_text, re.IGNORECASE)
                if chunk_matches:
                    stats["chunks"] = int(chunk_matches[-1])
                    break

        # Determine Tree-sitter info from semantic categories
        if stats["semantic_categories"]:
            function_count = stats["semantic_categories"].get("function", 0)
            if function_count > 0:
                stats["tree_sitter_info"] = f"{function_count} functions detected"
            else:
                stats["tree_sitter_info"] = "Structure-based chunking"

        # Extract processing time from result text if not found in stats
        if stats["processing_time"] == "unknown":
            time_matches = re.findall(r'Processing time:\s*([0-9.]+)s', result_text)
            if time_matches:
                stats["processing_time"] = f"{time_matches[0]}s"

        # Add deletion info if available
        if deletion_stats:
            chunks_deleted = deletion_stats.get('chunkss_deleted', 0)  # Note: typo in API response
            if chunks_deleted > 0:
                stats["chunks_deleted"] = chunks_deleted

        # Get actual embedding model name
        try:
            from embedding_config import embedding_config_manager
            current_model = embedding_config_manager.get_current_model()
            config = embedding_config_manager.get_current_config()
            stats["embedding_info"] = f"{current_model} ({config.dimensions}D, {config.max_tokens:,} tokens)"
        except Exception:
            stats["embedding_info"] = "Configurable embedding model"

    except Exception as e:
        print(f"   ⚠️ Statistics parsing error: {e}")

    return stats

def main():
    """Main bulk rebuild function"""
    print("🚀 Starting Bulk Rebuild of All Vector Databases")
    print("=" * 60)

    # Get current chunk counts for comparison
    print("📊 Getting current chunk counts...")
    current_chunks = get_current_chunk_counts()
    total_current = sum(current_chunks.values())

    # Get all codebases
    codebases = get_all_codebases()
    if not codebases:
        print("❌ No codebases found!")
        return

    print(f"📚 Found {len(codebases)} codebases:")
    for i, codebase in enumerate(codebases, 1):
        current_count = current_chunks.get(codebase, 0)
        print(f"   {i}. {codebase} (current: {current_count} chunks)")

    print(f"\n📊 **Current Total Chunks**: {total_current:,}")
    print(f"\n🔄 Starting rebuild process...")
    print(f"⏰ Timeout per codebase: {TIMEOUT_PER_CODEBASE}s")
    print(f"🚫 Excluding directories: {', '.join(EXCLUDE_DIRS)}")
    
    # Track results
    results = []
    successful = 0
    failed = 0
    start_time = time.time()
    
    # Rebuild each codebase
    for i, codebase in enumerate(codebases, 1):
        print(f"\n{'='*60}")
        print(f"📦 Processing {i}/{len(codebases)}: {codebase}")
        print(f"{'='*60}")
        
        result = rebuild_codebase(codebase)
        results.append(result)
        
        if result["success"]:
            successful += 1
        else:
            failed += 1
        
        # Show progress
        progress = (i / len(codebases)) * 100
        print(f"📊 Progress: {progress:.1f}% ({i}/{len(codebases)})")
    
    # Final summary
    total_time = time.time() - start_time
    print(f"\n{'='*60}")
    print("🎉 BULK REBUILD COMPLETE!")
    print(f"{'='*60}")
    print(f"📊 **Summary:**")
    print(f"   • Total codebases: {len(codebases)}")
    print(f"   • ✅ Successful: {successful}")
    print(f"   • ❌ Failed: {failed}")
    print(f"   • ⏰ Total time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
    
    # Detailed results with enhanced statistics
    print(f"\n📋 **Detailed Results:**")
    total_chunks = 0
    language_summary = {}
    tree_sitter_summary = {}

    for result in results:
        status = "✅" if result["success"] else "❌"
        codebase = result["codebase"]
        if result["success"]:
            stats = result.get("stats", {})
            chunks = stats.get("chunks", "unknown")
            languages = stats.get("languages", [])
            tree_sitter = stats.get("tree_sitter_info", "unknown")

            print(f"   {status} {codebase}: {chunks} chunks")
            print(f"      🌍 Languages: {', '.join(languages) if languages else 'unknown'}")
            print(f"      🌳 Tree-sitter: {tree_sitter}")

            # Accumulate statistics
            if isinstance(chunks, int):
                total_chunks += chunks

            for lang in languages:
                language_summary[lang] = language_summary.get(lang, 0) + 1

            if tree_sitter != "unknown":
                tree_sitter_summary[codebase] = tree_sitter

        else:
            error = result.get("error", "Unknown error")
            print(f"   {status} {codebase}: {error}")

    # Summary statistics
    print(f"\n📊 **Enhanced Statistics Summary:**")
    print(f"   • Total Chunks Generated: {total_chunks:,}")
    print(f"   • Languages Processed: {len(language_summary)}")
    print(f"   • Tree-sitter Enhanced: {len(tree_sitter_summary)} codebases")

    if language_summary:
        print(f"\n🌍 **Language Distribution:**")
        for lang, count in sorted(language_summary.items()):
            print(f"   • {lang}: {count} codebase(s)")

    if tree_sitter_summary:
        print(f"\n🌳 **Tree-sitter Function Detection:**")
        for codebase, info in tree_sitter_summary.items():
            print(f"   • {codebase}: {info}")

    # Before/After Comparison
    print(f"\n📈 **Before/After Comparison:**")
    print(f"   • Before Rebuild: {total_current:,} total chunks")
    print(f"   • After Rebuild: {total_chunks:,} total chunks")

    if total_chunks > total_current:
        improvement = ((total_chunks - total_current) / total_current) * 100
        print(f"   • 📈 Improvement: +{total_chunks - total_current:,} chunks ({improvement:.1f}% increase)")
    elif total_chunks < total_current:
        reduction = ((total_current - total_chunks) / total_current) * 100
        print(f"   • 📉 Optimization: -{total_current - total_chunks:,} chunks ({reduction:.1f}% reduction)")
    else:
        print(f"   • ➡️ No change in total chunk count")

    # Individual codebase improvements
    print(f"\n📊 **Individual Codebase Changes:**")
    for result in results:
        if result["success"]:
            codebase = result["codebase"]
            stats = result.get("stats", {})
            new_chunks = stats.get("chunks", 0)
            old_chunks = current_chunks.get(codebase, 0)

            if isinstance(new_chunks, int) and old_chunks > 0:
                if new_chunks > old_chunks:
                    change = ((new_chunks - old_chunks) / old_chunks) * 100
                    print(f"   📈 {codebase}: {old_chunks} → {new_chunks} (+{change:.1f}%)")
                elif new_chunks < old_chunks:
                    change = ((old_chunks - new_chunks) / old_chunks) * 100
                    print(f"   📉 {codebase}: {old_chunks} → {new_chunks} (-{change:.1f}%)")
                else:
                    print(f"   ➡️ {codebase}: {old_chunks} → {new_chunks} (no change)")
            else:
                print(f"   ❓ {codebase}: {old_chunks} → {new_chunks} (data incomplete)")

    # Tree-sitter benefits
    cpp_codebases = ["utils", "modbus", "test_project", "networking_project"]
    cpp_rebuilt = [r for r in results if r["codebase"] in cpp_codebases and r["success"]]

    if cpp_rebuilt:
        print(f"\n🌳 **Tree-sitter Benefits:**")
        print(f"   • C/C++ codebases with function-level chunking: {len(cpp_rebuilt)}")
        for result in cpp_rebuilt:
            print(f"     - {result['codebase']}: Enhanced with function-level granularity")

    print(f"\n🚀 All vector databases have been rebuilt with enhanced Tree-sitter integration!")
    print(f"🔧 Configurable embedding models and token-aware chunking now active!")

if __name__ == "__main__":
    main()
