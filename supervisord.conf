[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:code-analyzer-server]
command=uvicorn main:app --host 0.0.0.0 --port 5002 --reload
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/code-analyzer-server.err.log
stdout_logfile=/var/log/supervisor/code-analyzer-server.out.log
environment=PYTHONUNBUFFERED="1"

[program:web-management-server]
command=watchmedo auto-restart --directory=/app --pattern=*.py --recursive -- python3 web_management_server.py
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/web-management-server.err.log
stdout_logfile=/var/log/supervisor/web-management-server.out.log
environment=PYTHONUNBUFFERED="1",CODE_ANALYZER_BASE_URL="http://localhost:5002"

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
