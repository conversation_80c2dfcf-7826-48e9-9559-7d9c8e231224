#!/usr/bin/env python3
"""
Test script for discover_available_gpus() method
"""

import asyncio
import sys
import logging
from gpu_infrastructure import BasicGPUManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_discover_available_gpus():
    """Test the discover_available_gpus method"""
    print("🔍 Testing discover_available_gpus() method...")
    print("=" * 50)
    
    try:
        # Create GPU manager instance
        gpu_manager = BasicGPUManager()
        print("✅ GPU Manager created successfully")
        
        # Test method existence
        if hasattr(gpu_manager, 'discover_available_gpus'):
            print("✅ discover_available_gpus method exists")
        else:
            print("❌ discover_available_gpus method not found")
            return False
        
        # Test method call with timeout
        print("\n🚀 Calling discover_available_gpus()...")
        print("⏱️ This may take up to 15 seconds (method has built-in timeout)...")
        
        start_time = asyncio.get_event_loop().time()
        available_gpus = await gpu_manager.discover_available_gpus()
        end_time = asyncio.get_event_loop().time()
        
        execution_time = end_time - start_time
        print(f"⏱️ Method completed in {execution_time:.2f} seconds")
        
        # Analyze results
        print(f"\n📊 Results:")
        print(f"   Type: {type(available_gpus)}")
        print(f"   GPU count: {len(available_gpus)}")
        
        if available_gpus:
            print(f"   Available GPUs:")
            for host, gpu_info in available_gpus.items():
                print(f"     • {host}: {gpu_info}")
        else:
            print("   No GPUs found (this is normal in test environments)")
        
        # Test return type
        if isinstance(available_gpus, dict):
            print("✅ Method returns correct type (dict)")
        else:
            print(f"❌ Method returns incorrect type: {type(available_gpus)}")
            return False
        
        print("\n✅ discover_available_gpus() test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_gpu_manager_methods():
    """Test other GPU manager methods for completeness"""
    print("\n🔧 Testing other GPU Manager methods...")
    print("=" * 50)
    
    try:
        gpu_manager = BasicGPUManager()
        
        # Test get_supported_gpu_types
        supported_types = gpu_manager.get_supported_gpu_types()
        print(f"✅ Supported GPU types ({len(supported_types)}): {supported_types}")
        
        # Test get_gpu_specification for a known type
        if supported_types:
            test_type = supported_types[0]
            spec = gpu_manager.get_gpu_specification(test_type)
            print(f"✅ GPU specification for {test_type}: {spec}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU Manager methods test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 GPU Manager Test Suite")
    print("=" * 50)
    
    # Test discover_available_gpus
    test1_passed = await test_discover_available_gpus()
    
    # Test other methods
    test2_passed = await test_gpu_manager_methods()
    
    # Summary
    print("\n📋 Test Summary:")
    print("=" * 50)
    print(f"discover_available_gpus test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Other methods test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
