#!/usr/bin/env python3
"""
Test Embedding-Aware Chunking System
Tests the new embedding model configuration and token-aware chunking
"""

import requests
import json
from embedding_config import embedding_config_manager, get_embedding_config

def test_embedding_config():
    """Test embedding configuration system"""
    print("🔧 Testing Embedding Configuration System")
    print("=" * 60)
    
    # Test current model
    current_config = embedding_config_manager.get_current_config()
    print(f"📊 Current Model: {current_config.name}")
    print(f"   • Max Tokens: {current_config.max_tokens:,}")
    print(f"   • Safe Chunk Size: {current_config.safe_chunk_size:,}")
    print(f"   • Dimensions: {current_config.dimensions}")
    print(f"   • Description: {current_config.description}")
    
    # Test all available models
    print(f"\n📚 Available Models:")
    models = embedding_config_manager.list_available_models()
    for name, config in models.items():
        status = "✅ CURRENT" if name == current_config.name else "⚪"
        print(f"   {status} {name}: {config.max_tokens:,} tokens, {config.dimensions}D")
    
    # Test token estimation
    test_text = "def hello_world():\n    print('Hello, World!')\n    return True"
    tokens = embedding_config_manager.estimate_tokens(test_text)
    is_valid = embedding_config_manager.is_chunk_size_valid(test_text)
    
    print(f"\n🧪 Token Estimation Test:")
    print(f"   • Text: {len(test_text)} characters")
    print(f"   • Estimated Tokens: {tokens}")
    print(f"   • Valid for {current_config.name}: {'✅ Yes' if is_valid else '❌ No'}")

def test_chunking_with_different_models():
    """Test chunking behavior with different embedding models"""
    print(f"\n🌳 Testing Tree-sitter Chunking with Different Models")
    print("=" * 60)
    
    # Test models with different token limits
    test_models = ["nomic-embed-text", "mxbai-embed-large", "all-minilm"]
    
    for model_name in test_models:
        config = get_embedding_config(model_name)
        print(f"\n📊 Model: {model_name}")
        print(f"   • Max Tokens: {config.max_tokens:,}")
        print(f"   • Safe Chunk Size: {config.safe_chunk_size:,}")
        
        # Simulate chunking behavior
        if config.max_tokens >= 8000:
            print(f"   • Strategy: Large chunks (functions + context)")
        elif config.max_tokens >= 1000:
            print(f"   • Strategy: Medium chunks (functions only)")
        else:
            print(f"   • Strategy: Small chunks (function fragments)")

def test_api_endpoints():
    """Test the embedding API endpoints"""
    print(f"\n🌐 Testing Embedding API Endpoints")
    print("=" * 60)
    
    base_url = "http://home-ai-server.local:5002"
    
    # Test endpoints
    endpoints = [
        "/embedding/current",
        "/embedding/models", 
        "/embedding/stats"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint}: Success")
                if endpoint == "/embedding/current":
                    model_name = data.get("model_name", "unknown")
                    print(f"   • Current Model: {model_name}")
                elif endpoint == "/embedding/models":
                    models = data.get("available_models", {})
                    print(f"   • Available Models: {len(models)}")
                elif endpoint == "/embedding/stats":
                    token_limits = data.get("token_limits", {})
                    max_tokens = token_limits.get("max_tokens", 0)
                    print(f"   • Max Tokens: {max_tokens:,}")
            else:
                print(f"❌ {endpoint}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def test_chunk_validation():
    """Test chunk size validation"""
    print(f"\n✅ Testing Chunk Size Validation")
    print("=" * 60)
    
    # Test different chunk sizes
    test_chunks = [
        ("Small function", "def small(): return 42"),
        ("Medium function", "def medium():\n" + "    # Comment\n" * 10 + "    return True"),
        ("Large function", "def large():\n" + "    # Very long function\n" * 100 + "    return result")
    ]
    
    config = embedding_config_manager.get_current_config()
    
    for name, chunk_text in test_chunks:
        tokens = embedding_config_manager.estimate_tokens(chunk_text)
        is_valid = embedding_config_manager.is_chunk_size_valid(chunk_text)
        
        status = "✅" if is_valid else "❌"
        print(f"{status} {name}:")
        print(f"   • Characters: {len(chunk_text)}")
        print(f"   • Estimated Tokens: {tokens}")
        print(f"   • Valid: {'Yes' if is_valid else 'No'}")
        print(f"   • Limit: {config.safe_chunk_size:,} tokens")

def main():
    """Main test function"""
    print("🚀 Embedding-Aware Chunking System Test")
    print("=" * 80)
    
    try:
        test_embedding_config()
        test_chunking_with_different_models()
        test_chunk_validation()
        test_api_endpoints()
        
        print(f"\n🎉 All Tests Completed!")
        print("=" * 80)
        
        # Summary
        config = embedding_config_manager.get_current_config()
        print(f"\n📊 **Current Configuration Summary:**")
        print(f"   • Model: {config.name}")
        print(f"   • Max Tokens: {config.max_tokens:,}")
        print(f"   • Safe Chunk Size: {config.safe_chunk_size:,}")
        print(f"   • Dimensions: {config.dimensions}")
        print(f"   • Context Window: {config.context_window:,}")
        
        print(f"\n🎯 **Key Benefits:**")
        print(f"   ✅ Token-aware chunking prevents embedding model overflow")
        print(f"   ✅ Configurable embedding models for different use cases")
        print(f"   ✅ Automatic chunk size validation and filtering")
        print(f"   ✅ Optimized for {config.name} with {config.safe_chunk_size:,} token limit")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
