#!/usr/bin/env python3
"""
Simple test for LYNN-PC llama3:latest model with Code Analyzer tool
"""

import requests
import json

def test_lynn_pc_connectivity():
    """Test basic connectivity to lynn-pc Ollama"""
    print("🔍 Testing lynn-pc Ollama Connectivity")
    print("-" * 40)
    
    lynn_pc_url = "http://192.168.0.32:11434"
    
    try:
        # Test basic connectivity
        response = requests.get(f"{lynn_pc_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ lynn-pc Ollama accessible")
            print(f"   Available models: {len(models.get('models', []))}")
            
            for model in models.get('models', []):
                name = model.get('name', 'unknown')
                print(f"   • {name}")
            
            return True
        else:
            print(f"❌ lynn-pc Ollama error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ lynn-pc Ollama connection failed: {e}")
        return False

def test_code_analyzer_server():
    """Test code analyzer server"""
    print(f"\n🌐 Testing Code Analyzer Server")
    print("-" * 40)
    
    try:
        response = requests.get("http://************:5002/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Code analyzer server accessible")
            print(f"   Collections: {len(data.get('collections', []))}")
            
            collections = data.get('collections', [])
            for collection in collections[:5]:  # Show first 5
                print(f"   • {collection}")
            
            return True
        else:
            print(f"❌ Code analyzer server error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Code analyzer server failed: {e}")
        return False

def test_lynn_pc_model():
    """Test the LYNN-PC llama3:latest model with code analyzer tool"""
    print(f"\n🧪 Testing LYNN-PC llama3:latest Model")
    print("-" * 40)
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "LYNN-PC llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test query
    query = "list codebases"
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": query}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    print(f"📤 Testing query: '{query}'")
    print(f"   Model: {model}")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        print(f"📥 Response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            message = data.get("choices", [{}])[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            print(f"   Content length: {len(content)} chars")
            print(f"   Tool calls: {len(tool_calls)}")
            
            # Show first 200 chars
            preview = content[:200] + "..." if len(content) > 200 else content
            print(f"   Preview: {preview}")
            
            # Analyze response
            real_codebases = ["utils", "z80emu", "library-management-system", "test_project"]
            found_real = [cb for cb in real_codebases if cb.lower() in content.lower()]
            
            generic_indicators = ["wordpress", "react", "popular", "open-source"]
            found_generic = [gi for gi in generic_indicators if gi.lower() in content.lower()]
            
            print(f"\n🔍 Analysis:")
            if tool_calls:
                print(f"   ✅ Tool was called ({len(tool_calls)} calls)")
                for i, tc in enumerate(tool_calls):
                    func_name = tc.get("function", {}).get("name", "unknown")
                    print(f"      Tool {i+1}: {func_name}")
            elif found_real:
                print(f"   ✅ Tool working - found real codebases: {found_real}")
            elif found_generic:
                print(f"   ❌ Tool not working - generic response detected: {found_generic}")
            else:
                print(f"   ❓ Unclear - no clear indicators found")
            
            return len(tool_calls) > 0 or len(found_real) > 0
            
        elif response.status_code == 400:
            print(f"   ❌ Bad Request")
            try:
                error = response.json()
                print(f"   Error: {error}")
            except:
                print(f"   Error text: {response.text[:200]}")
            return False
            
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def test_tool_configuration():
    """Test if tool is properly configured"""
    print(f"\n🔧 Testing Tool Configuration")
    print("-" * 40)
    
    # This is a basic check - in a real scenario you'd need to access OpenWebUI's internal API
    print("💡 Manual checks needed:")
    print("1. Go to OpenWebUI → Workspace → Tools → code_analyzer_tool")
    print("2. Verify server URL is: http://************:5002")
    print("3. Go to Workspace → Models → LYNN-PC llama3:latest")
    print("4. Verify code_analyzer_tool is enabled")

def main():
    print("🚀 LYNN-PC Model Debug Test")
    print("=" * 50)
    
    # Test connectivity
    lynn_pc_ok = test_lynn_pc_connectivity()
    analyzer_ok = test_code_analyzer_server()
    
    if not lynn_pc_ok:
        print("\n❌ lynn-pc Ollama not accessible")
        print("🔧 Fix: Ensure Ollama is running on lynn-pc with OLLAMA_HOST=0.0.0.0")
        return
    
    if not analyzer_ok:
        print("\n❌ Code analyzer server not accessible")
        print("🔧 Fix: Ensure code analyzer server is running on home-ai-server")
        return
    
    # Test the model
    tool_working = test_lynn_pc_model()
    
    # Show configuration check
    test_tool_configuration()
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    print(f"✅ lynn-pc Ollama: {'Working' if lynn_pc_ok else 'Failed'}")
    print(f"✅ Code Analyzer Server: {'Working' if analyzer_ok else 'Failed'}")
    print(f"🛠️ Tool with LYNN-PC model: {'Working' if tool_working else 'Not Working'}")
    
    if not tool_working:
        print(f"\n🔧 TROUBLESHOOTING:")
        print("1. Check tool is enabled for LYNN-PC llama3:latest model")
        print("2. Verify tool server URL is correct (http://************:5002)")
        print("3. Check OpenWebUI logs for errors")
        print("4. Try restarting OpenWebUI")

if __name__ == "__main__":
    main()
