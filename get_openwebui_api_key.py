#!/usr/bin/env python3
"""
Helper script to get a valid OpenWebUI API key
"""

import requests
import json
import sys
import getpass

def get_api_key_interactive():
    """Get API key through interactive login"""
    print("🔑 OpenWebUI API Key Helper")
    print("=" * 40)
    
    openwebui_url = "http://home-ai-server.local:8080"
    
    print(f"OpenWebUI URL: {openwebui_url}")
    print()
    print("To get your API key:")
    print("1. Open your browser and go to: http://home-ai-server.local:8080")
    print("2. Log in to OpenWebUI")
    print("3. Go to Settings > Account")
    print("4. Look for 'API Keys' section")
    print("5. Generate a new API key or copy existing one")
    print()
    
    # Try to check if OpenWebUI is accessible
    try:
        response = requests.get(openwebui_url, timeout=5)
        if response.status_code == 200:
            print("✅ OpenWebUI server is accessible")
        else:
            print(f"⚠️ OpenWebUI returned HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot connect to OpenWebUI: {e}")
        return None
    
    print()
    api_key = input("Enter your API key from OpenWebUI Settings > Account: ").strip()
    
    if not api_key:
        print("❌ No API key provided")
        return None
    
    # Test the API key
    print("\n🧪 Testing API key...")
    return test_api_key(openwebui_url, api_key)

def test_api_key(openwebui_url, api_key):
    """Test if the API key works"""
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Test with models endpoint
        response = requests.get(f"{openwebui_url}/api/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ API key is valid!")
            print(f"   Found {len(models)} models available")
            
            # Show first few models
            if models:
                print("   Available models:")
                for model in models[:3]:
                    model_id = model.get('id', 'unknown')
                    print(f"     • {model_id}")
                if len(models) > 3:
                    print(f"     ... and {len(models) - 3} more")
            
            return api_key
            
        elif response.status_code == 401:
            print("❌ API key is invalid or expired")
            print("   Please get a new API key from OpenWebUI Settings > Account")
            return None
        else:
            print(f"❌ API test failed: HTTP {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return None
            
    except Exception as e:
        print(f"❌ Error testing API key: {e}")
        return None

def save_api_key(api_key):
    """Save API key to a config file"""
    config = {
        "openwebui_url": "http://home-ai-server.local:8080",
        "api_key": api_key
    }
    
    try:
        with open("openwebui_config.json", "w") as f:
            json.dump(config, f, indent=2)
        print(f"\n💾 API key saved to openwebui_config.json")
        print("   You can now use this config in your tests")
        return True
    except Exception as e:
        print(f"❌ Failed to save config: {e}")
        return False

def load_api_key():
    """Load API key from config file"""
    try:
        with open("openwebui_config.json", "r") as f:
            config = json.load(f)
        return config.get("api_key"), config.get("openwebui_url")
    except FileNotFoundError:
        return None, None
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return None, None

def main():
    """Main function"""
    print("🚀 OpenWebUI API Key Management")
    print("=" * 50)
    
    # Try to load existing API key
    existing_key, existing_url = load_api_key()
    
    if existing_key:
        print("📁 Found existing API key configuration")
        test_existing = input("Test existing API key? (y/n): ").strip().lower()
        
        if test_existing == 'y':
            print("\n🧪 Testing existing API key...")
            if test_api_key(existing_url or "http://home-ai-server.local:8080", existing_key):
                print("✅ Existing API key is still valid!")
                print(f"   API Key: {existing_key[:20]}...")
                return existing_key
            else:
                print("❌ Existing API key is no longer valid")
    
    # Get new API key
    print("\n🔑 Getting new API key...")
    api_key = get_api_key_interactive()
    
    if api_key:
        save_api_key(api_key)
        print(f"\n🎉 Success! Your API key is ready to use:")
        print(f"   API Key: {api_key[:20]}...")
        print(f"\n📋 Update your test files with this API key:")
        print(f'   self.api_key = "{api_key}"')
        return api_key
    else:
        print("\n❌ Failed to get valid API key")
        print("\n💡 Troubleshooting:")
        print("1. Make sure OpenWebUI is running on port 8080")
        print("2. Log in to OpenWebUI web interface")
        print("3. Go to Settings > Account")
        print("4. Generate a new API key")
        print("5. Copy the key exactly as shown")
        return None

if __name__ == "__main__":
    try:
        api_key = main()
        sys.exit(0 if api_key else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Script crashed: {e}")
        sys.exit(1)
