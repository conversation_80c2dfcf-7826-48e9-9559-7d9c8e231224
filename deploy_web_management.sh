#!/bin/bash
# Deployment script for Code Analyzer with Web Management Interface
# Handles the correct directory structure

set -e  # Exit on any error

echo "🚀 CODE ANALYZER WEB MANAGEMENT DEPLOYMENT"
echo "=========================================="

# Check if we're in the correct directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: docker-compose.yml not found in current directory"
    echo "   Please run this script from ~/home-ai-system/"
    exit 1
fi

if [ ! -d "source_code" ]; then
    echo "❌ Error: source_code directory not found"
    echo "   Expected directory structure:"
    echo "   ~/home-ai-system/"
    echo "   ├── docker-compose.yml"
    echo "   ├── source_code/"
    echo "   └── code_analyzer_server/"
    exit 1
fi

if [ ! -d "code_analyzer_server" ]; then
    echo "❌ Error: code_analyzer_server directory not found"
    echo "   Please ensure the code analyzer source is in ./code_analyzer_server/"
    exit 1
fi

echo "✅ Directory structure verified"
echo "   📁 docker-compose.yml: $(pwd)/docker-compose.yml"
echo "   📁 source_code: $(pwd)/source_code"
echo "   📁 code_analyzer_server: $(pwd)/code_analyzer_server"

# Check required files in code_analyzer_server
echo ""
echo "🔍 Checking required files..."

REQUIRED_FILES=(
    "code_analyzer_server/main.py"
    "code_analyzer_server/web_management_server.py"
    "code_analyzer_server/templates/dashboard.html"
    "code_analyzer_server/Dockerfile"
    "code_analyzer_server/supervisord.conf"
    "code_analyzer_server/requirements.txt"
)

MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (missing)"
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo ""
    echo "❌ Missing required files:"
    for file in "${MISSING_FILES[@]}"; do
        echo "   • $file"
    done
    echo ""
    echo "Please ensure all required files are in place before deployment."
    exit 1
fi

# Check if jinja2 is in requirements.txt
echo ""
echo "🔍 Checking requirements.txt..."
if grep -q "jinja2" code_analyzer_server/requirements.txt; then
    echo "   ✅ jinja2 dependency found"
else
    echo "   ⚠️ Adding jinja2 to requirements.txt..."
    echo "jinja2==3.1.2" >> code_analyzer_server/requirements.txt
    echo "   ✅ jinja2 dependency added"
fi

# Stop existing container
echo ""
echo "🛑 Stopping existing container..."
docker-compose down || echo "   ℹ️ No existing container to stop"

# Build with no cache to ensure fresh build
echo ""
echo "🔨 Building Docker image..."
docker-compose build --no-cache

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "✅ Docker image built successfully"

# Start the services
echo ""
echo "🚀 Starting services..."
docker-compose up -d

if [ $? -ne 0 ]; then
    echo "❌ Failed to start services"
    exit 1
fi

echo "✅ Services started successfully"

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo ""
echo "🏥 Checking service health..."

# Check Code Analyzer Server
if curl -f -s http://localhost:5002/health > /dev/null; then
    echo "   ✅ Code Analyzer Server (port 5002): Healthy"
    ANALYZER_OK=true
else
    echo "   ❌ Code Analyzer Server (port 5002): Not responding"
    ANALYZER_OK=false
fi

# Check Web Management Interface
if curl -f -s http://localhost:5003/api/health > /dev/null; then
    echo "   ✅ Web Management Interface (port 5003): Healthy"
    WEB_OK=true
else
    echo "   ❌ Web Management Interface (port 5003): Not responding"
    WEB_OK=false
fi

# Show container status
echo ""
echo "📊 Container status:"
docker ps | grep code-analyzer-server || echo "   ❌ Container not running"

# Show logs if there are issues
if [ "$ANALYZER_OK" = false ] || [ "$WEB_OK" = false ]; then
    echo ""
    echo "🔍 Recent container logs:"
    echo "----------------------------------------"
    docker logs --tail 20 code-analyzer-server
    echo "----------------------------------------"
fi

# Final status
echo ""
echo "=========================================="
if [ "$ANALYZER_OK" = true ] && [ "$WEB_OK" = true ]; then
    echo "🎉 DEPLOYMENT SUCCESSFUL!"
    echo ""
    echo "🔗 Access Points:"
    echo "   • Code Analyzer API: http://localhost:5002"
    echo "   • Web Management Dashboard: http://localhost:5003"
    echo ""
    echo "📊 Available Features:"
    echo "   • Real-time server monitoring"
    echo "   • Codebase management interface"
    echo "   • GPU infrastructure status"
    echo "   • Query testing and performance metrics"
    echo ""
    echo "🔧 Management Commands:"
    echo "   • View logs: docker logs code-analyzer-server"
    echo "   • Check services: docker exec code-analyzer-server supervisorctl status"
    echo "   • Restart services: docker-compose restart"
    echo ""
    echo "✅ Both services are operational!"
else
    echo "⚠️ DEPLOYMENT COMPLETED WITH ISSUES"
    echo ""
    echo "❌ Some services are not responding properly"
    echo "🔧 Troubleshooting:"
    echo "   • Check logs: docker logs code-analyzer-server"
    echo "   • Check supervisor: docker exec code-analyzer-server supervisorctl status"
    echo "   • Restart container: docker-compose restart"
    echo ""
    if [ "$ANALYZER_OK" = false ]; then
        echo "   • Code Analyzer Server (port 5002) needs attention"
    fi
    if [ "$WEB_OK" = false ]; then
        echo "   • Web Management Interface (port 5003) needs attention"
    fi
fi

echo "=========================================="
