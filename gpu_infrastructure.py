"""
Basic GPU Infrastructure for Phase 0
Implements Section 5 from TODO_SOFTWARE.md - Basic GPU discovery and coordination
"""

import asyncio
import aiohttp
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)

class GPUTier(Enum):
    """GPU performance tiers"""
    BASIC = "basic"
    GOOD = "good"
    EXCELLENT = "excellent"
    PREMIUM = "premium"

@dataclass
class GPUSpecification:
    """GPU hardware specifications"""
    gpu_type: str
    architecture: str
    vram: str
    tier: GPUTier
    processing_speed: float
    batch_size: int
    parallel_requests: int
    cost_per_hour: float
    reliability_score: float
    shared_resource: bool

    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            "gpu_type": self.gpu_type,
            "architecture": self.architecture,
            "vram": self.vram,
            "tier": self.tier.value,  # Convert enum to string
            "processing_speed": self.processing_speed,
            "batch_size": self.batch_size,
            "parallel_requests": self.parallel_requests,
            "cost_per_hour": self.cost_per_hour,
            "reliability_score": self.reliability_score,
            "shared_resource": self.shared_resource
        }

class BasicGPUManager:
    """Basic GPU discovery and classification for Phase 0 foundation"""
    
    def __init__(self):
        self.known_gpu_types = {
            "tesla_m40": GPUSpecification(
                gpu_type="tesla_m40",
                architecture="Maxwell 2015",
                vram="24GB",
                tier=GPUTier.BASIC,
                processing_speed=1.0,
                batch_size=1,
                parallel_requests=1,
                cost_per_hour=0.0,
                reliability_score=0.95,
                shared_resource=False
            ),
            "tesla_p40": GPUSpecification(
                gpu_type="tesla_p40",
                architecture="Pascal 2016",
                vram="24GB",
                tier=GPUTier.GOOD,
                processing_speed=2.0,
                batch_size=2,
                parallel_requests=2,
                cost_per_hour=0.0,
                reliability_score=0.97,
                shared_resource=False
            ),
            "tesla_p40_dual": GPUSpecification(
                gpu_type="tesla_p40_dual",
                architecture="Pascal 2016 Dual",
                vram="48GB",
                tier=GPUTier.EXCELLENT,
                processing_speed=4.5,
                batch_size=8,
                parallel_requests=6,
                cost_per_hour=0.0,
                reliability_score=0.98,
                shared_resource=False
            ),
            "rtx_3070": GPUSpecification(
                gpu_type="rtx_3070",
                architecture="Ampere 2020",
                vram="8GB",
                tier=GPUTier.GOOD,
                processing_speed=4.0,
                batch_size=3,
                parallel_requests=2,
                cost_per_hour=0.05,
                reliability_score=0.98,
                shared_resource=True
            ),
            "rtx_3080": GPUSpecification(
                gpu_type="rtx_3080",
                architecture="Ampere 2020",
                vram="10GB",
                tier=GPUTier.EXCELLENT,
                processing_speed=5.0,
                batch_size=4,
                parallel_requests=3,
                cost_per_hour=0.10,
                reliability_score=0.98,
                shared_resource=True
            ),
            "rtx_3090": GPUSpecification(
                gpu_type="rtx_3090",
                architecture="Ampere 2020",
                vram="24GB",
                tier=GPUTier.PREMIUM,
                processing_speed=10.0,
                batch_size=6,
                parallel_requests=4,
                cost_per_hour=0.20,
                reliability_score=0.98,
                shared_resource=True
            ),
            "rtx_3050_mobile": GPUSpecification(
                gpu_type="rtx_3050_mobile",
                architecture="Ampere 2022",
                vram="4GB",
                tier=GPUTier.BASIC,
                processing_speed=2.5,
                batch_size=2,
                parallel_requests=1,
                cost_per_hour=0.02,
                reliability_score=0.95,
                shared_resource=True
            ),
            "gtx_1060": GPUSpecification(
                gpu_type="gtx_1060",
                architecture="Pascal 2016",
                vram="6GB",
                tier=GPUTier.BASIC,
                processing_speed=1.5,
                batch_size=1,
                parallel_requests=1,
                cost_per_hour=0.0,
                reliability_score=0.92,
                shared_resource=False
            ),
            "unknown_gpu": GPUSpecification(
                gpu_type="unknown_gpu",
                architecture="Unknown",
                vram="Unknown",
                tier=GPUTier.BASIC,
                processing_speed=1.0,
                batch_size=1,
                parallel_requests=1,
                cost_per_hour=0.0,
                reliability_score=0.80,
                shared_resource=False
            )
        }
    
    async def discover_available_gpus(self, enable_network_scan: bool = False) -> Dict[str, Dict[str, Any]]:
        """Enhanced GPU discovery with optional network scanning and static configuration"""

        # Static GPU nodes (known machines with specific GPU types)
        static_gpu_nodes = [
            {"host": "http://home-ai-server.local:11434", "type": "tesla_m40"},
            {"host": "http://tesla-p40:11434", "type": "tesla_p40"},
            {"host": "http://tmw-ai-server:11434", "type": "tesla_p40_dual"},
            {"host": "http://rtx3070-win:11434", "type": "rtx_3070"},
            {"host": "http://rtx3080-win:11434", "type": "rtx_3080"},
            {"host": "http://t5810:11434", "type": "rtx_3090"},
            {"host": "http://x99-mach:11434", "type": "rtx_3090"},
            {"host": "http://lynn-pc:11434", "type": "rtx_3050_mobile"},
            {"host": "http://home-game-server:11434", "type": "gtx_1060"}
        ]

        # Optionally perform network scanning for additional Ollama instances
        scanned_nodes = []
        if enable_network_scan:
            logger.info("Network scanning enabled - looking for additional Ollama instances")
            scanned_nodes = await self._scan_network_for_ollama()
        else:
            logger.info("Network scanning disabled - using static configuration only")

        # Combine static and scanned nodes (static takes precedence for GPU type identification)
        gpu_nodes = static_gpu_nodes + scanned_nodes
        
        available_gpus = {}

        # Create tasks for concurrent GPU discovery with timeout protection
        async def check_single_gpu(node):
            try:
                # Add timeout protection for individual GPU checks
                if await asyncio.wait_for(self.check_gpu_basic_availability(node["host"]), timeout=5):
                    gpu_spec = self.known_gpu_types[node["type"]]
                    logger.info(f"Discovered available GPU: {node['type']} at {node['host']}")
                    return node["host"], {
                        "type": node["type"],
                        "tier": gpu_spec.tier.value,
                        "specifications": gpu_spec.to_dict(),  # Convert to dict for JSON serialization
                        "host": node["host"],
                        "status": "available"
                    }
                else:
                    logger.debug(f"GPU not available: {node['type']} at {node['host']}")
                    return None, None
            except asyncio.TimeoutError:
                logger.debug(f"GPU discovery timeout for {node['host']}")
                return None, None
            except asyncio.CancelledError:
                logger.debug(f"GPU discovery cancelled for {node['host']}")
                return None, None
            except Exception as e:
                logger.debug(f"Error checking GPU {node['host']}: {e}")
                return None, None

        # Run GPU checks concurrently with improved error handling
        try:
            tasks = [check_single_gpu(node) for node in gpu_nodes]

            # Remove overall timeout to prevent partial results
            logger.info(f"Starting discovery of {len(gpu_nodes)} GPU nodes...")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            successful_discoveries = 0
            failed_discoveries = 0

            for i, result in enumerate(results):
                node = gpu_nodes[i]
                if isinstance(result, tuple) and result[0] is not None:
                    host, gpu_info = result
                    available_gpus[host] = gpu_info
                    successful_discoveries += 1
                    logger.info(f"✅ GPU discovered: {node['type']} at {node['host']}")
                elif isinstance(result, Exception):
                    failed_discoveries += 1
                    logger.warning(f"❌ GPU discovery failed for {node['type']} at {node['host']}: {result}")
                else:
                    failed_discoveries += 1
                    logger.warning(f"❌ GPU discovery returned None for {node['type']} at {node['host']}")

            logger.info(f"GPU discovery completed: {successful_discoveries} successful, {failed_discoveries} failed")

        except Exception as e:
            logger.error(f"GPU discovery failed with exception: {e}")

        logger.info(f"Final result: {len(available_gpus)} GPUs available")
        for host, gpu_info in available_gpus.items():
            logger.info(f"  • {gpu_info['type']} ({gpu_info['tier']}) at {host}")

        return available_gpus

    async def _scan_network_for_ollama(self) -> List[Dict[str, str]]:
        """Scan local network for additional Ollama instances on port 11434"""
        scanned_nodes = []

        try:
            import socket
            import ipaddress

            # Get local network range
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

            # Only scan a small range around the current IP for safety
            # This prevents scanning 256 IPs and finding false positives
            base_ip = ipaddress.IPv4Address(local_ip)
            network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)

            # Scan only a limited range (e.g., +/- 20 IPs from current)
            scan_range = []
            for offset in range(-20, 21):
                try:
                    candidate_ip = base_ip + offset
                    if candidate_ip in network and str(candidate_ip) != local_ip:
                        scan_range.append(str(candidate_ip))
                except (ValueError, ipaddress.AddressValueError):
                    continue

            logger.info(f"Scanning {len(scan_range)} IPs around {local_ip} for Ollama instances...")

            # Create scanning tasks for the limited range
            scan_tasks = [self._check_ollama_on_ip(ip) for ip in scan_range]

            # Limit concurrent scans to avoid overwhelming the network
            semaphore = asyncio.Semaphore(10)

            async def limited_scan(task):
                async with semaphore:
                    return await task

            # Run scans with limited concurrency and timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*[limited_scan(task) for task in scan_tasks], return_exceptions=True),
                    timeout=30  # Max 30 seconds for network scan
                )

                # Collect successful discoveries (be very strict)
                for result in results:
                    if isinstance(result, dict) and result.get('host') and result.get('type'):
                        # Additional validation: ensure it's actually Ollama
                        if await self._validate_ollama_instance(result['host']):
                            scanned_nodes.append(result)
                            logger.info(f"Discovered valid Ollama instance: {result['host']}")

            except asyncio.TimeoutError:
                logger.warning("Network scan timed out after 30 seconds")

            logger.info(f"Network scan completed: found {len(scanned_nodes)} additional Ollama instances")

        except Exception as e:
            logger.warning(f"Network scanning failed: {e}")

        return scanned_nodes

    async def _validate_ollama_instance(self, host: str) -> bool:
        """Validate that a host is actually running Ollama (not just responding on port 11434)"""
        try:
            timeout = aiohttp.ClientTimeout(total=3)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Check for Ollama-specific endpoints
                async with session.get(f"{host}/api/version") as response:
                    if response.status == 200:
                        data = await response.json()
                        # Check if response looks like Ollama
                        return 'version' in data or 'ollama' in str(data).lower()
        except Exception:
            pass
        return False

    async def _check_ollama_on_ip(self, ip: str) -> Optional[Dict[str, str]]:
        """Check if Ollama is running on a specific IP"""
        host = f"http://{ip}:11434"

        try:
            timeout = aiohttp.ClientTimeout(total=2, connect=1)  # Fast scan
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(f"{host}/api/tags") as response:
                    if response.status == 200:
                        # Try to identify GPU type from response or use generic
                        return {
                            "host": host,
                            "type": "unknown_gpu",  # Will be classified later
                            "discovered_via": "network_scan"
                        }
        except Exception:
            pass  # Expected for most IPs that don't have Ollama

        return None

    async def check_gpu_basic_availability(self, gpu_host: str) -> bool:
        """Basic check if Ollama is responding on the host"""
        try:
            timeout = aiohttp.ClientTimeout(total=3, connect=2)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(f"{gpu_host}/api/tags") as response:
                    return response.status == 200
        except asyncio.TimeoutError:
            logger.debug(f"GPU {gpu_host} timeout")
            return False
        except asyncio.CancelledError:
            logger.debug(f"GPU {gpu_host} check cancelled")
            return False
        except Exception as e:
            logger.debug(f"GPU availability check failed for {gpu_host}: {e}")
            return False
    
    def get_gpu_specification(self, gpu_type: str) -> Optional[GPUSpecification]:
        """Get specifications for a GPU type"""
        return self.known_gpu_types.get(gpu_type)
    
    def get_supported_gpu_types(self) -> List[str]:
        """Get list of supported GPU types"""
        return list(self.known_gpu_types.keys())

class BasicProcessingCoordinator:
    """Basic processing coordination for Phase 0"""
    
    def __init__(self):
        self.gpu_manager = BasicGPUManager()
        self.default_models = {
            "tesla_m40": "smollm2:1.7b",
            "tesla_p40": "codellama:7b-instruct",
            "tesla_p40_dual": "deepseek-coder:33b",
            "rtx_3070": "codellama:7b-instruct",
            "rtx_3080": "deepseek-coder:6.7b",
            "rtx_3090": "deepseek-coder:33b",
            "rtx_3050_mobile": "smollm2:1.7b",
            "gtx_1060": "smollm2:1.7b"
        }
        
    async def select_basic_processing_gpu(self) -> Dict[str, Any]:
        """Simple GPU selection for Phase 0 implementation"""
        available_gpus = await self.gpu_manager.discover_available_gpus()
        
        if not available_gpus:
            raise Exception("No GPUs available for processing")
        
        # Simple selection: prefer faster GPUs if available
        gpu_priority = ["rtx_3090", "tesla_p40_dual", "rtx_3080", "rtx_3070", "tesla_p40", "tesla_m40"]
        
        for preferred_type in gpu_priority:
            for host, info in available_gpus.items():
                if info["type"] == preferred_type:
                    return {
                        "host": host,
                        "type": info["type"],
                        "model": self.default_models[info["type"]],
                        "specifications": info["specifications"],
                        "tier": info["tier"]
                    }
        
        # Fallback to any available GPU
        first_gpu = next(iter(available_gpus.items()))
        host, info = first_gpu
        return {
            "host": host,
            "type": info["type"],
            "model": self.default_models[info["type"]],
            "specifications": info["specifications"],
            "tier": info["tier"]
        }
    
    async def estimate_processing_time(self, chunk_count: int, gpu_selection: Dict[str, Any]) -> float:
        """Estimate processing time for given chunks and GPU"""
        gpu_spec = gpu_selection["specifications"]

        # Basic time estimation based on GPU speed and chunk count
        base_time_per_chunk = 2.0  # seconds per chunk baseline
        speed_multiplier = gpu_spec["processing_speed"]  # Access as dict
        batch_efficiency = min(gpu_spec["batch_size"], chunk_count) / chunk_count

        estimated_time = (chunk_count * base_time_per_chunk) / (speed_multiplier * (1 + batch_efficiency))

        return max(estimated_time, 1.0)  # Minimum 1 second
    
    async def get_processing_recommendations(self, chunk_count: int) -> Dict[str, Any]:
        """Get processing recommendations for given workload"""
        try:
            gpu_selection = await self.select_basic_processing_gpu()
            estimated_time = await self.estimate_processing_time(chunk_count, gpu_selection)
            
            return {
                "recommended_gpu": gpu_selection,
                "estimated_time_seconds": estimated_time,
                "estimated_time_minutes": estimated_time / 60,
                "chunk_count": chunk_count,
                "processing_strategy": "single_gpu_sequential",
                "cost_estimate": gpu_selection["specifications"]["cost_per_hour"] * (estimated_time / 3600)
            }
        except Exception as e:
            logger.error(f"Failed to get processing recommendations: {e}")
            return {
                "error": str(e),
                "fallback_strategy": "local_processing_only"
            }

# Integration with existing framework
class GPUProcessingStage:
    """Processing stage that can utilize GPU infrastructure"""
    
    def __init__(self, coordinator: BasicProcessingCoordinator):
        self.coordinator = coordinator
    
    async def prepare_gpu_processing(self, chunk_count: int) -> Dict[str, Any]:
        """Prepare GPU processing for given workload"""
        recommendations = await self.coordinator.get_processing_recommendations(chunk_count)
        
        if "error" in recommendations:
            logger.warning(f"GPU processing not available: {recommendations['error']}")
            return {"gpu_available": False, "fallback_required": True}
        
        logger.info(f"GPU processing prepared: {recommendations['recommended_gpu']['type']} "
                   f"(~{recommendations['estimated_time_minutes']:.1f} minutes)")
        
        return {
            "gpu_available": True,
            "gpu_selection": recommendations["recommended_gpu"],
            "estimated_time": recommendations["estimated_time_seconds"],
            "cost_estimate": recommendations["cost_estimate"]
        }

def create_basic_gpu_infrastructure() -> tuple[BasicGPUManager, BasicProcessingCoordinator]:
    """Factory function to create basic GPU infrastructure"""
    gpu_manager = BasicGPUManager()
    processing_coordinator = BasicProcessingCoordinator()
    
    logger.info("Created basic GPU infrastructure")
    return gpu_manager, processing_coordinator
